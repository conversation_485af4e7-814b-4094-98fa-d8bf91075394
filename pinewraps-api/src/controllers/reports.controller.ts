import type { Request, Response } from 'express';
import { prisma } from '../lib/prisma';
import { ApiError } from '../utils/api-error';
import { formatISO, subDays, parseISO, startOfDay, endOfDay, format } from 'date-fns';
import { OrderStatus, PaymentStatus, POSOrderStatus, POSPaymentStatus, Prisma } from '@prisma/client';

import { stringify } from 'csv-stringify/sync';

export class ReportsController {
  /**
   * Get sales overview summary
   */
  static async getSalesSummary(req: Request, res: Response) {
    try {
      const { fromDate, toDate } = req.query;

      if (!fromDate || !toDate) {
        throw new ApiError(400, 'fromDate and toDate are required');
      }

      const startDate = startOfDay(parseISO(fromDate as string));
      const endDate = endOfDay(parseISO(toDate as string));

      // Calculate previous period (same length as selected period)
      const periodLength = endDate.getTime() - startDate.getTime();
      const previousStartDate = new Date(startDate.getTime() - periodLength);
      const previousEndDate = new Date(endDate.getTime() - periodLength);

      // Get online orders summary - include COMPLETED orders
      // Get registered customer count for the selected date range
      const registeredCustomerCount = await prisma.customer.count({
        where: {
          createdAt: { 
            gte: startDate,
            lte: endDate 
          }
        }
      });
      
      // Get unique customers from POS orders based on phone numbers
      const posCustomersResult = await prisma.$queryRaw`
        SELECT COUNT(DISTINCT "customerPhone") as unique_pos_customers
        FROM "POSOrder"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        AND "customerPhone" IS NOT NULL
      `;
      
      // Extract the count from the result
      const posCustomerCount = posCustomersResult[0]?.unique_pos_customers || 0;
      
      // Calculate total unique customers (registered + POS customers)
      // Note: This might count some customers twice if they are both registered and made POS orders
      const customerCount = registeredCustomerCount + Number(posCustomerCount);

      // Define valid order statuses for sales reporting
      const validOrderStatuses = [
        OrderStatus.PROCESSING,
        OrderStatus.READY_FOR_PICKUP,
        OrderStatus.OUT_FOR_DELIVERY,
        OrderStatus.DELIVERED,
        OrderStatus.COMPLETED
      ];

      // Define valid POS order statuses for sales reporting (all except CANCELLED and REFUNDED)
      const validPOSOrderStatuses = [
        POSOrderStatus.PENDING,
        POSOrderStatus.DESIGN_QUEUE,
        POSOrderStatus.DESIGN_PROCESSING,
        POSOrderStatus.DESIGN_READY,
        POSOrderStatus.KITCHEN_QUEUE,
        POSOrderStatus.KITCHEN_PROCESSING,
        POSOrderStatus.KITCHEN_READY,
        POSOrderStatus.FINAL_CHECK_QUEUE,
        POSOrderStatus.FINAL_CHECK_PROCESSING,
        POSOrderStatus.COMPLETED,
        POSOrderStatus.PARALLEL_PROCESSING,
        POSOrderStatus.PARTIALLY_REFUNDED
      ];

      // Get payment methods distribution for online orders
      const onlinePaymentMethods = await prisma.payment.groupBy({
        by: ['paymentMethod'],
        _count: { _all: true },
        where: {
          order: {
            status: { in: validOrderStatuses },
            createdAt: { gte: startDate, lte: endDate },
          },
          status: PaymentStatus.CAPTURED,
        },
      });

      // Get online orders stats for current period
      const onlineOrdersStats = await prisma.order.aggregate({
        _count: { id: true },
        _sum: {
          total: true,
          deliveryCharge: true,
          discountAmount: true
        },
        where: {
          createdAt: { gte: startDate, lte: endDate },
          status: { in: validOrderStatuses },
          payment: {
            status: PaymentStatus.CAPTURED // Only include orders with successful payments
          }
        },
      });

      // Get payment methods distribution for POS orders
      const posPaymentMethods = await prisma.pOSPayment.groupBy({
        by: ['method'],
        _count: { _all: true },
        where: {
          order: {
            status: { in: validPOSOrderStatuses },
            createdAt: { gte: startDate, lte: endDate },
          },
          status: { in: [POSPaymentStatus.PENDING, POSPaymentStatus.PARTIALLY_PAID, POSPaymentStatus.FULLY_PAID] },
        },
      });

      // Get POS orders stats for current period (excluding partially refunded orders for now)
      const posOrdersStatsExcludingPartialRefunds = await prisma.pOSOrder.aggregate({
        _count: { id: true },
        _sum: {
          total: true,
          deliveryCharge: true,
          couponDiscount: true
        },
        where: {
          createdAt: { gte: startDate, lte: endDate },
          status: { in: validPOSOrderStatuses.filter(status => status !== POSOrderStatus.PARTIALLY_REFUNDED) },
          payments: {
            some: {
              status: { in: [POSPaymentStatus.FULLY_PAID, POSPaymentStatus.PARTIALLY_PAID, POSPaymentStatus.PENDING] } // Include all relevant payment statuses
            }
          }
        },
      });

      // Get partially refunded orders separately to calculate net sales
      const partiallyRefundedOrders = await prisma.pOSOrder.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          status: POSOrderStatus.PARTIALLY_REFUNDED,
          payments: {
            some: {
              status: { in: [POSPaymentStatus.FULLY_PAID, POSPaymentStatus.PARTIALLY_PAID, POSPaymentStatus.PENDING] }
            }
          }
        },
        select: {
          id: true,
          total: true,
          deliveryCharge: true,
          couponDiscount: true,
          payments: {
            select: {
              amount: true
            }
          }
        }
      });

      // Calculate net sales for partially refunded orders
      let partiallyRefundedNetSales = 0;
      let partiallyRefundedNetDeliveryCharge = 0;
      let partiallyRefundedNetCouponDiscount = 0;
      let totalPartialRefunds = 0;

      partiallyRefundedOrders.forEach(order => {
        const refundedAmount = order.payments
          .filter(payment => payment.amount < 0)
          .reduce((sum, payment) => sum + Math.abs(payment.amount), 0);

        const netOrderTotal = Math.max(0, order.total - refundedAmount);
        partiallyRefundedNetSales += netOrderTotal;
        partiallyRefundedNetDeliveryCharge += order.deliveryCharge || 0;
        partiallyRefundedNetCouponDiscount += order.couponDiscount || 0;
        totalPartialRefunds += refundedAmount;
      });

      // Combine stats
      const posOrdersStats = {
        _count: {
          id: (posOrdersStatsExcludingPartialRefunds._count.id || 0) + partiallyRefundedOrders.length
        },
        _sum: {
          total: (posOrdersStatsExcludingPartialRefunds._sum.total || 0) + partiallyRefundedNetSales,
          deliveryCharge: (posOrdersStatsExcludingPartialRefunds._sum.deliveryCharge || 0) + partiallyRefundedNetDeliveryCharge,
          couponDiscount: (posOrdersStatsExcludingPartialRefunds._sum.couponDiscount || 0) + partiallyRefundedNetCouponDiscount
        }
      };

      // Get previous period online orders
      const previousOnlineOrdersStats = await prisma.order.aggregate({
        _count: { id: true },
        _sum: { 
          total: true,
          deliveryCharge: true,
          discountAmount: true
        },
        where: {
          createdAt: { gte: previousStartDate, lte: previousEndDate },
          status: { in: validOrderStatuses },
          payment: {
            status: PaymentStatus.CAPTURED // Only include orders with successful payments
          }
        },
      });

      // Get previous period POS orders (excluding partially refunded orders for now)
      const previousPosOrdersStatsExcludingPartialRefunds = await prisma.pOSOrder.aggregate({
        _count: { id: true },
        _sum: {
          total: true,
          deliveryCharge: true,
          couponDiscount: true
        },
        where: {
          createdAt: { gte: previousStartDate, lte: previousEndDate },
          status: { in: validPOSOrderStatuses.filter(status => status !== POSOrderStatus.PARTIALLY_REFUNDED) },
          payments: {
            some: {
              status: { in: [POSPaymentStatus.FULLY_PAID, POSPaymentStatus.PARTIALLY_PAID, POSPaymentStatus.PENDING] } // Include all relevant payment statuses
            }
          }
        },
      });

      // Get previous period partially refunded orders
      const previousPartiallyRefundedOrders = await prisma.pOSOrder.findMany({
        where: {
          createdAt: { gte: previousStartDate, lte: previousEndDate },
          status: POSOrderStatus.PARTIALLY_REFUNDED,
          payments: {
            some: {
              status: { in: [POSPaymentStatus.FULLY_PAID, POSPaymentStatus.PARTIALLY_PAID, POSPaymentStatus.PENDING] }
            }
          }
        },
        select: {
          id: true,
          total: true,
          deliveryCharge: true,
          couponDiscount: true,
          payments: {
            select: {
              amount: true
            }
          }
        }
      });

      // Calculate net sales for previous period partially refunded orders
      let previousPartiallyRefundedNetSales = 0;
      let previousPartiallyRefundedNetDeliveryCharge = 0;
      let previousPartiallyRefundedNetCouponDiscount = 0;

      previousPartiallyRefundedOrders.forEach(order => {
        const refundedAmount = order.payments
          .filter(payment => payment.amount < 0)
          .reduce((sum, payment) => sum + Math.abs(payment.amount), 0);

        const netOrderTotal = Math.max(0, order.total - refundedAmount);
        previousPartiallyRefundedNetSales += netOrderTotal;
        previousPartiallyRefundedNetDeliveryCharge += order.deliveryCharge || 0;
        previousPartiallyRefundedNetCouponDiscount += order.couponDiscount || 0;
      });

      // Combine previous period stats
      const previousPosOrdersStats = {
        _count: {
          id: (previousPosOrdersStatsExcludingPartialRefunds._count.id || 0) + previousPartiallyRefundedOrders.length
        },
        _sum: {
          total: (previousPosOrdersStatsExcludingPartialRefunds._sum.total || 0) + previousPartiallyRefundedNetSales,
          deliveryCharge: (previousPosOrdersStatsExcludingPartialRefunds._sum.deliveryCharge || 0) + previousPartiallyRefundedNetDeliveryCharge,
          couponDiscount: (previousPosOrdersStatsExcludingPartialRefunds._sum.couponDiscount || 0) + previousPartiallyRefundedNetCouponDiscount
        }
      };

      // Get online payment method stats
      const onlinePaymentMethodStats = await prisma.$queryRaw`
        SELECT
          p."paymentMethod" as "paymentMethod",
          COUNT(DISTINCT o.id) as "orderCount",
          SUM(o.total) as "totalAmount"
        FROM "Order" o
        JOIN "Payment" p ON o.id = p."orderId"
        WHERE
          o."createdAt" >= ${startDate}
          AND o."createdAt" <= ${endDate}
          AND o.status = 'COMPLETED'
        GROUP BY p."paymentMethod"
      `;

      // Get POS payment method stats
      const posPaymentMethodStats = await prisma.$queryRaw`
        SELECT
          pp."method" as "paymentMethod",
          COUNT(DISTINCT po.id) as "orderCount",
          SUM(po.total) as "totalAmount"
        FROM "POSOrder" po
        JOIN "POSPayment" pp ON po.id = pp."orderId"
        WHERE
          po."createdAt" >= ${startDate}
          AND po."createdAt" <= ${endDate}
          AND po.status = 'COMPLETED'
        GROUP BY pp."method"
      `;

      // Keep online and POS payment stats separate
      const onlinePaymentMethodMap = new Map();
      const posPaymentMethodMap = new Map();

      // Process online payment methods
      (onlinePaymentMethodStats as any[]).forEach(item => {
        if (!item.paymentMethod) return;

        const method = String(item.paymentMethod);
        onlinePaymentMethodMap.set(method, {
          orderCount: Number(item.orderCount) || 0,
          totalAmount: parseFloat(item.totalAmount) || 0,
          source: 'online'
        });
      });

      // Process POS payment methods
      (posPaymentMethodStats as any[]).forEach(item => {
        if (!item.paymentMethod) return;

        const method = String(item.paymentMethod);
        posPaymentMethodMap.set(method, {
          orderCount: Number(item.orderCount) || 0,
          totalAmount: parseFloat(item.totalAmount) || 0,
          source: 'pos'
        });
      });
      
      // Create a combined map for backward compatibility
      const paymentMethodMap = new Map([...onlinePaymentMethodMap, ...posPaymentMethodMap]);

      // Calculate totals
      const totalOrders = (onlineOrdersStats._count.id || 0) + (posOrdersStats._count.id || 0);
      const totalSales = (onlineOrdersStats._sum.total || 0) + (posOrdersStats._sum.total || 0);
      const onlineSales = onlineOrdersStats._sum.total || 0;
      const posSales = posOrdersStats._sum.total || 0;

      const previousPeriodOrders = (previousOnlineOrdersStats._count.id || 0) + (previousPosOrdersStats._count.id || 0);
      const previousPeriodSales = (previousOnlineOrdersStats._sum.total || 0) + (previousPosOrdersStats._sum.total || 0);

      // Calculate average order value
      const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

      // Calculate delivery fees (using deliveryCharge field which exists in both models)
      const deliveryFees = (onlineOrdersStats._sum.deliveryCharge || 0) + (posOrdersStats._sum.deliveryCharge || 0);

      // Calculate discounts
      const totalDiscounts = (onlineOrdersStats._sum.discountAmount || 0) + (posOrdersStats._sum.couponDiscount || 0);

      // Process payment methods from our Maps, keeping online and POS separate
      const onlinePaymentMethodData = Array.from(onlinePaymentMethodMap.entries()).map(([method, data]) => ({
        method,
        count: data.orderCount,
        amount: data.totalAmount,
        percentage: onlineSales > 0 ? (data.totalAmount / onlineSales) * 100 : 0,
        source: 'online'
      }));
      
      const posPaymentMethodData = Array.from(posPaymentMethodMap.entries()).map(([method, data]) => ({
        method,
        count: data.orderCount,
        amount: data.totalAmount,
        percentage: posSales > 0 ? (data.totalAmount / posSales) * 100 : 0,
        source: 'pos'
      }));
      
      // Combined payment methods data (for backward compatibility)
      const paymentMethodData = [
        ...onlinePaymentMethodData,
        ...posPaymentMethodData
      ];

      // Get refunds from POS payments (negative amounts typically indicate refunds)
      const refundPayments = await prisma.pOSPayment.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          amount: { lt: 0 }, // Negative amounts indicate refunds
          status: { in: [POSPaymentStatus.FULLY_PAID, POSPaymentStatus.PARTIALLY_PAID, POSPaymentStatus.PENDING] } // Include all payment statuses for refunds
        },
        select: {
          amount: true,
          orderId: true
        }
      });
      
      // Get all refunded POS orders
      const refundedPosOrders = await prisma.pOSOrder.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          status: { in: [POSOrderStatus.REFUNDED, POSOrderStatus.PARTIALLY_REFUNDED] }
        },
        select: {
          id: true,
          total: true
        }
      });
      
      // Get all refunded online orders
      const refundedOnlineOrders = await prisma.order.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          status: OrderStatus.REFUNDED
        },
        select: {
          id: true,
          total: true
        }
      });
      
      // Calculate total refunds from all sources
      const refundPaymentsTotal = refundPayments.reduce((sum, refund) => sum + Math.abs(refund.amount || 0), 0);
      const refundedPosOrdersTotal = refundedPosOrders.reduce((sum, order) => sum + (order.total || 0), 0);
      const refundedOnlineOrdersTotal = refundedOnlineOrders.reduce((sum, order) => sum + (order.total || 0), 0);

      // Add partial refunds from current period
      const totalRefunds = refundPaymentsTotal + refundedPosOrdersTotal + refundedOnlineOrdersTotal + totalPartialRefunds;

      return res.json({
        success: true,
        data: {
          // Summary
          totalSales,
          totalOrders,
          averageOrderValue,
          onlineSales,
          posSales,
          previousPeriodSales,
          previousPeriodOrders,

          // Additional metrics
          totalCustomers: customerCount || 0,
          refundAmount: totalRefunds,
          deliveryFees,
          totalDiscounts,

          // Payment methods (separated by source)
          paymentMethods: paymentMethodData,
          onlinePaymentMethods: onlinePaymentMethodData,
          posPaymentMethods: posPaymentMethodData,

          // Raw data for further processing
          _raw: {
            onlineOrders: onlineOrdersStats,
            posOrders: posOrdersStats,
            refunds: { 
              _sum: { amount: totalRefunds },
              refundPaymentsTotal,
              refundedPosOrdersTotal,
              refundedOnlineOrdersTotal,
              refundedPosOrders: refundedPosOrders.length,
              refundedOnlineOrders: refundedOnlineOrders.length
            },
            discounts: totalDiscounts
          }
        },
      });
    } catch (error) {
      console.error('Error getting sales summary:', error);
      if (error instanceof ApiError) {
        return res.status(error.statusCode).json({
          success: false,
          error: {
            message: error.message,
          },
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get sales summary',
        },
      });
    }
  }

  /**
   * Get daily sales data
   */
  static async getDailySales(req: Request, res: Response) {
    try {
      const { fromDate, toDate } = req.query;

      if (!fromDate || !toDate) {
        throw new ApiError(400, 'fromDate and toDate are required');
      }

      const startDate = startOfDay(parseISO(fromDate as string));
      const endDate = endOfDay(parseISO(toDate as string));

      // Get online orders grouped by date - include all non-cancelled, non-refunded orders
      const onlineOrdersByDate = await prisma.$queryRaw`
        SELECT
          DATE("createdAt") as date,
          SUM(total) as sales,
          SUM("deliveryCharge") as deliveryCharge,
          COUNT(*) as orders
        FROM "Order"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        AND status IN ('PROCESSING', 'READY_FOR_PICKUP', 'OUT_FOR_DELIVERY', 'DELIVERED', 'COMPLETED')
        AND "paymentStatus" = 'CAPTURED'
        GROUP BY DATE("createdAt")
        ORDER BY date ASC
      `;
      
      // Get registered customers grouped by date
      const registeredCustomersByDate = await prisma.$queryRaw`
        SELECT
          DATE("createdAt") as date,
          COUNT(*) as customers
        FROM "Customer"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        GROUP BY DATE("createdAt")
        ORDER BY date ASC
      `;
      
      // Get unique POS customers grouped by date
      const posCustomersByDate = await prisma.$queryRaw`
        SELECT
          DATE("createdAt") as date,
          COUNT(DISTINCT "customerPhone") as customers
        FROM "POSOrder"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        AND "customerPhone" IS NOT NULL
        GROUP BY DATE("createdAt")
        ORDER BY date ASC
      `;
      
      // Get refunds from POS payments grouped by date
      const posRefundsByDate = await prisma.$queryRaw`
        SELECT
          DATE("createdAt") as date,
          SUM(ABS(amount)) as refunds
        FROM "POSPayment"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        AND amount < 0
        GROUP BY DATE("createdAt")
        ORDER BY date ASC
      `;
      
      // Get refunds from refunded POS orders grouped by date
      const posOrderRefundsByDate = await prisma.$queryRaw`
        SELECT
          DATE("createdAt") as date,
          SUM(total) as refunds
        FROM "POSOrder"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        AND status IN ('REFUNDED', 'PARTIALLY_REFUNDED')
        GROUP BY DATE("createdAt")
        ORDER BY date ASC
      `;
      
      // Get refunds from refunded online orders grouped by date
      const onlineOrderRefundsByDate = await prisma.$queryRaw`
        SELECT
          DATE("createdAt") as date,
          SUM(total) as refunds
        FROM "Order"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        AND status = 'REFUNDED'
        GROUP BY DATE("createdAt")
        ORDER BY date ASC
      `;

      // Get POS orders grouped by date - include all non-cancelled, non-refunded orders (excluding partially refunded for now)
      const posOrdersByDate = await prisma.$queryRaw`
        SELECT
          DATE("createdAt") as date,
          SUM(total) as sales,
          SUM("deliveryCharge") as deliveryCharge,
          COUNT(*) as orders
        FROM "POSOrder"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        AND status IN ('PENDING', 'DESIGN_QUEUE', 'DESIGN_PROCESSING', 'DESIGN_READY',
                     'KITCHEN_QUEUE', 'KITCHEN_PROCESSING', 'KITCHEN_READY',
                     'FINAL_CHECK_QUEUE', 'FINAL_CHECK_PROCESSING', 'COMPLETED',
                     'PARALLEL_PROCESSING')
        GROUP BY DATE("createdAt")
        ORDER BY date ASC
      `;

      // Get partially refunded orders grouped by date with payment details
      const partiallyRefundedOrdersByDate = await prisma.pOSOrder.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          status: 'PARTIALLY_REFUNDED'
        },
        select: {
          id: true,
          createdAt: true,
          total: true,
          deliveryCharge: true,
          payments: {
            select: {
              amount: true
            }
          }
        }
      });

      // Process partially refunded orders and group by date
      const partiallyRefundedByDateMap = new Map();
      partiallyRefundedOrdersByDate.forEach(order => {
        const dateStr = formatISO(new Date(order.createdAt), { representation: 'date' });
        const refundedAmount = order.payments
          .filter(payment => payment.amount < 0)
          .reduce((sum, payment) => sum + Math.abs(payment.amount), 0);

        const netOrderTotal = Math.max(0, order.total - refundedAmount);

        if (!partiallyRefundedByDateMap.has(dateStr)) {
          partiallyRefundedByDateMap.set(dateStr, {
            sales: 0,
            deliveryCharge: 0,
            orders: 0,
            refunds: 0
          });
        }

        const dayData = partiallyRefundedByDateMap.get(dateStr);
        dayData.sales += netOrderTotal;
        dayData.deliveryCharge += order.deliveryCharge || 0;
        dayData.orders += 1;
        dayData.refunds += refundedAmount;
      });

      // Combine online and POS data
      const dateMap = new Map();

      // Create a refund map to aggregate all refund sources by date
      const refundMap = new Map();

      // Process POS payment refunds
      (posRefundsByDate as any[]).forEach(item => {
        const dateStr = formatISO(new Date(item.date), { representation: 'date' });
        refundMap.set(dateStr, (refundMap.get(dateStr) || 0) + Number(item.refunds || 0));
      });

      // Process POS order refunds
      (posOrderRefundsByDate as any[]).forEach(item => {
        const dateStr = formatISO(new Date(item.date), { representation: 'date' });
        refundMap.set(dateStr, (refundMap.get(dateStr) || 0) + Number(item.refunds || 0));
      });

      // Add partial refunds to refund map
      partiallyRefundedByDateMap.forEach((data, dateStr) => {
        refundMap.set(dateStr, (refundMap.get(dateStr) || 0) + data.refunds);
      });
      
      // Process online order refunds
      (onlineOrderRefundsByDate as any[]).forEach(item => {
        const dateStr = formatISO(new Date(item.date), { representation: 'date' });
        refundMap.set(dateStr, (refundMap.get(dateStr) || 0) + Number(item.refunds || 0));
      });
      
      // Create a map to track customer counts by date
      const customerCountMap = new Map<string, number>();
      
      // Process registered customer data
      (registeredCustomersByDate as any[]).forEach(item => {
        const dateStr = formatISO(new Date(item.date), { representation: 'date' });
        customerCountMap.set(dateStr, Number(item.customers) || 0);
      });
      
      // Process POS customer data and add to the customer count
      (posCustomersByDate as any[]).forEach(item => {
        const dateStr = formatISO(new Date(item.date), { representation: 'date' });
        const existingCount = customerCountMap.get(dateStr) || 0;
        customerCountMap.set(dateStr, existingCount + Number(item.customers) || 0);
      });
      
      // Initialize the date map with combined customer data
      // Process all dates from both registered and POS customers
      const allDates = new Set<string>();
      (registeredCustomersByDate as any[]).forEach(item => {
        allDates.add(formatISO(new Date(item.date), { representation: 'date' }));
      });
      (posCustomersByDate as any[]).forEach(item => {
        allDates.add(formatISO(new Date(item.date), { representation: 'date' }));
      });
      
      // Initialize the date map with all dates that have customer data
      allDates.forEach(dateStr => {
        dateMap.set(dateStr, {
          date: dateStr,
          sales: 0,
          orders: 0,
          onlineSales: 0,
          onlineOrders: 0,
          posSales: 0,
          posOrders: 0,
          deliveryFees: 0,
          customers: customerCountMap.get(dateStr) || 0,
          refunds: refundMap.get(dateStr) || 0,
        });
      });

      // Process online orders
      (onlineOrdersByDate as any[]).forEach(item => {
        const dateStr = formatISO(new Date(item.date), { representation: 'date' });
        if (dateMap.has(dateStr)) {
          const existing = dateMap.get(dateStr);
          dateMap.set(dateStr, {
            ...existing,
            sales: Number(item.sales) || 0,
            orders: Number(item.orders) || 0,
            onlineSales: Number(item.sales) || 0,
            onlineOrders: Number(item.orders) || 0,
            deliveryFees: Number(item.deliverycharge) || 0,
          });
        } else {
          dateMap.set(dateStr, {
            date: dateStr,
            sales: Number(item.sales) || 0,
            orders: Number(item.orders) || 0,
            onlineSales: Number(item.sales) || 0,
            onlineOrders: Number(item.orders) || 0,
            posSales: 0,
            posOrders: 0,
            deliveryFees: Number(item.deliverycharge) || 0,
            customers: 0,
            refunds: refundMap.get(dateStr) || 0,
          });
        }
      });

      // Process POS orders
      (posOrdersByDate as any[]).forEach(item => {
        const dateStr = formatISO(new Date(item.date), { representation: 'date' });
        if (dateMap.has(dateStr)) {
          const existing = dateMap.get(dateStr);
          dateMap.set(dateStr, {
            ...existing,
            sales: existing.sales + (Number(item.sales) || 0),
            orders: existing.orders + (Number(item.orders) || 0),
            posSales: Number(item.sales) || 0,
            posOrders: Number(item.orders) || 0,
            deliveryFees: (existing.deliveryFees || 0) + (Number(item.deliverycharge) || 0),
          });
        } else {
          dateMap.set(dateStr, {
            date: dateStr,
            sales: Number(item.sales) || 0,
            orders: Number(item.orders) || 0,
            onlineSales: 0,
            onlineOrders: 0,
            posSales: Number(item.sales) || 0,
            posOrders: Number(item.orders) || 0,
            deliveryFees: Number(item.deliverycharge) || 0,
            customers: 0,
            refunds: refundMap.get(dateStr) || 0,
          });
        }
      });

      // Process partially refunded POS orders
      partiallyRefundedByDateMap.forEach((partialData, dateStr) => {
        if (dateMap.has(dateStr)) {
          const existing = dateMap.get(dateStr);
          dateMap.set(dateStr, {
            ...existing,
            sales: existing.sales + partialData.sales,
            orders: existing.orders + partialData.orders,
            posSales: existing.posSales + partialData.sales,
            posOrders: existing.posOrders + partialData.orders,
            deliveryFees: (existing.deliveryFees || 0) + partialData.deliveryCharge,
          });
        } else {
          dateMap.set(dateStr, {
            date: dateStr,
            sales: partialData.sales,
            orders: partialData.orders,
            onlineSales: 0,
            onlineOrders: 0,
            posSales: partialData.sales,
            posOrders: partialData.orders,
            deliveryFees: partialData.deliveryCharge,
            customers: 0,
            refunds: refundMap.get(dateStr) || 0,
          });
        }
      });

      // Convert map to array and sort by date
      const result = Array.from(dateMap.values()).sort((a, b) =>
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      return res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Error getting daily sales:', error);
      if (error instanceof ApiError) {
        return res.status(error.statusCode).json({
          success: false,
          error: {
            message: error.message,
          },
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get daily sales',
        },
      });
    }
  }

  /**
   * Get top selling products
   */
  static async getTopProducts(req: Request, res: Response) {
    try {
      const { fromDate, toDate, limit = 5 } = req.query;

      if (!fromDate || !toDate) {
        throw new ApiError(400, 'fromDate and toDate are required');
      }

      const startDate = startOfDay(parseISO(fromDate as string));
      const endDate = endOfDay(parseISO(toDate as string));
      const limitNum = parseInt(limit as string) || 5;

      // Get top products from online orders
      const onlineTopProducts = await prisma.$queryRaw`
        SELECT
          p.id,
          p.name,
          p.visibility as product_visibility,
          SUM(oi.quantity) as quantity,
          SUM(oi.price * oi.quantity) as revenue
        FROM "OrderItem" oi
        JOIN "Order" o ON oi."orderId" = o.id
        JOIN "Product" p ON oi.name = p.name
        WHERE o."createdAt" >= ${startDate} AND o."createdAt" <= ${endDate}
        AND o.status = 'COMPLETED'
        GROUP BY p.id, p.name, p.visibility
      `;

      // Get top products from POS orders
      const posTopProducts = await prisma.$queryRaw`
        SELECT
          p.id,
          p.name,
          p.visibility as product_visibility,
          SUM(poi.quantity) as quantity,
          SUM(poi."unitPrice" * poi.quantity) as revenue
        FROM "POSOrderItem" poi
        JOIN "POSOrder" po ON poi."orderId" = po.id
        JOIN "Product" p ON poi."productName" = p.name
        WHERE po."createdAt" >= ${startDate} AND po."createdAt" <= ${endDate}
        AND po.status = 'COMPLETED'
        GROUP BY p.id, p.name, p.visibility
      `;

      // Combine online and POS products
      const productMap = new Map();

      // Process online products
      (onlineTopProducts as any[]).forEach(product => {
        productMap.set(product.id, {
          id: product.id,
          name: product.name,
          visibility: product.product_visibility,
          quantity: Number(product.quantity) || 0,
          revenue: Number(product.revenue) || 0,
        });
      });

      // Process POS products
      (posTopProducts as any[]).forEach(product => {
        if (productMap.has(product.id)) {
          const existing = productMap.get(product.id);
          productMap.set(product.id, {
            ...existing,
            quantity: existing.quantity + (Number(product.quantity) || 0),
            revenue: existing.revenue + (Number(product.revenue) || 0),
          });
        } else {
          productMap.set(product.id, {
            id: product.id,
            name: product.name,
            visibility: product.product_visibility,
            quantity: Number(product.quantity) || 0,
            revenue: Number(product.revenue) || 0,
          });
        }
      });

      // Convert map to array, sort by revenue, and limit
      const combinedProducts = Array.from(productMap.values())
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, limitNum);

      return res.json({
        success: true,
        data: combinedProducts,
      });
    } catch (error) {
      console.error('Error getting top products:', error);
      if (error instanceof ApiError) {
        return res.status(error.statusCode).json({
          success: false,
          error: {
            message: error.message,
          },
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get top products',
        },
      });
    }
  }

  /**
   * Get sales by category
   */
  static async getTopCategories(req: Request, res: Response) {
    try {
      const { fromDate, toDate, limit = 5 } = req.query;

      if (!fromDate || !toDate) {
        throw new ApiError(400, 'fromDate and toDate are required');
      }

      const startDate = startOfDay(parseISO(fromDate as string));
      const endDate = endOfDay(parseISO(toDate as string));
      const limitNum = parseInt(limit as string) || 5;

      // Get sales by category from online orders
      const onlineCategorySales = await prisma.$queryRaw`
        SELECT
          c.id,
          c.name as category,
          c.visibility as category_visibility,
          SUM(oi.price * oi.quantity) as sales
        FROM "OrderItem" oi
        JOIN "Order" o ON oi."orderId" = o.id
        JOIN "Product" p ON oi.name = p.name
        JOIN "Category" c ON p."categoryId" = c.id
        WHERE o."createdAt" >= ${startDate} AND o."createdAt" <= ${endDate}
        AND o.status = 'COMPLETED'
        GROUP BY c.id, c.name, c.visibility
      `;

      // Get sales by category from POS orders
      const posCategorySales = await prisma.$queryRaw`
        SELECT
          c.id,
          c.name as category,
          c.visibility as category_visibility,
          SUM(poi."unitPrice" * poi.quantity) as sales
        FROM "POSOrderItem" poi
        JOIN "POSOrder" po ON poi."orderId" = po.id
        JOIN "Product" p ON poi."productName" = p.name
        JOIN "Category" c ON p."categoryId" = c.id
        WHERE po."createdAt" >= ${startDate} AND po."createdAt" <= ${endDate}
        AND po.status = 'COMPLETED'
        GROUP BY c.id, c.name, c.visibility
      `;

      // Combine online and POS category sales
      const categoryMap = new Map();

      // Process online category sales
      (onlineCategorySales as any[]).forEach(category => {
        categoryMap.set(category.id, {
          id: category.id,
          category: category.category,
          visibility: category.category_visibility,
          sales: Number(category.sales) || 0,
        });
      });

      // Process POS category sales
      (posCategorySales as any[]).forEach(category => {
        if (categoryMap.has(category.id)) {
          const existing = categoryMap.get(category.id);
          categoryMap.set(category.id, {
            ...existing,
            sales: existing.sales + (Number(category.sales) || 0),
          });
        } else {
          categoryMap.set(category.id, {
            id: category.id,
            category: category.category,
            visibility: category.category_visibility,
            sales: Number(category.sales) || 0,
          });
        }
      });

      // Convert map to array, sort by sales, and limit
      const combinedCategories = Array.from(categoryMap.values())
        .sort((a, b) => b.sales - a.sales)
        .slice(0, limitNum);

      // Calculate total sales for percentage
      const totalSales = combinedCategories.reduce((sum, cat) => sum + cat.sales, 0);

      // Add percentage to each category
      const categoriesWithPercentage = combinedCategories.map(category => ({
        ...category,
        percentage: totalSales > 0 ? (category.sales / totalSales) * 100 : 0,
      }));

      return res.json({
        success: true,
        data: categoriesWithPercentage,
      });
    } catch (error) {
      console.error('Error getting category sales:', error);
      if (error instanceof ApiError) {
        return res.status(error.statusCode).json({
          success: false,
          error: {
            message: error.message,
          },
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get category sales',
        },
      });
    }
  }

  /**
   * Export report data to CSV
   */
  static async exportReport(req: Request, res: Response) {
    try {
      const { type, fromDate, toDate } = req.query;

      if (!type || !fromDate || !toDate) {
        throw new ApiError(400, 'type, fromDate, and toDate are required');
      }

      const startDate = startOfDay(parseISO(fromDate as string));
      const endDate = endOfDay(parseISO(toDate as string));
      const reportType = type as string;

      // Set headers for CSV download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=${reportType}-report-${format(startDate, 'yyyy-MM-dd')}-to-${format(endDate, 'yyyy-MM-dd')}.csv`);

      // Generate CSV based on report type
      switch (reportType) {
        case 'overview':
          await exportOverviewReport(res, startDate, endDate);
          break;
        case 'products':
          await exportProductsReport(res, startDate, endDate);
          break;
        case 'categories':
          await exportCategoriesReport(res, startDate, endDate);
          break;
        case 'payments':
          await exportPaymentsReport(res, startDate, endDate);
          break;
        case 'orders':
          await exportOrdersReport(res, startDate, endDate);
          break;
        case 'pos-orders':
          await exportPosOrdersReport(res, startDate, endDate);
          break;
        case 'purchase-orders':
          await exportPurchaseOrdersReport(res, startDate, endDate);
          break;
        default:
          throw new ApiError(400, `Unsupported report type: ${reportType}`);
      }
    } catch (error) {
      console.error('Error exporting report:', error);
      if (error instanceof ApiError) {
        return res.status(error.statusCode).json({
          success: false,
          error: {
            message: error.message,
          },
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          message: 'Failed to export report',
        },
      });
    }
  }
}

/**
 * Export overview report (daily sales)
 */
async function exportOverviewReport(res: Response, startDate: Date, endDate: Date) {
  // Get daily sales data
  const onlineOrdersByDate = await prisma.$queryRaw`
    SELECT
      DATE("createdAt") as date,
      SUM(total) as sales,
          SUM("deliveryCharge") as delivery_fees,
      COUNT(*) as orders
    FROM "Order"
    WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
    AND status = 'COMPLETED'
    GROUP BY DATE("createdAt")
    ORDER BY date ASC
  `;

  // Get POS orders grouped by date
  const posOrdersByDate = await prisma.$queryRaw`
    SELECT
      DATE("createdAt") as date,
      SUM(total) as sales,
          SUM("deliveryCharge") as delivery_fees,
      COUNT(*) as orders
    FROM "POSOrder"
    WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
    AND status = 'COMPLETED'
    GROUP BY DATE("createdAt")
    ORDER BY date ASC
  `;

  // Combine online and POS data
  const dateMap = new Map();

  // Process online orders
  (onlineOrdersByDate as any[]).forEach(item => {
    const dateStr = formatISO(new Date(item.date), { representation: 'date' });
    dateMap.set(dateStr, {
      date: dateStr,
      sales: Number(item.sales) || 0,
      orders: Number(item.orders) || 0,
      onlineSales: Number(item.sales) || 0,
      onlineOrders: Number(item.orders) || 0,
      posSales: 0,
      posOrders: 0,
    });
  });

  // Process POS orders
  (posOrdersByDate as any[]).forEach(item => {
    const dateStr = formatISO(new Date(item.date), { representation: 'date' });
    if (dateMap.has(dateStr)) {
      const existing = dateMap.get(dateStr);
      dateMap.set(dateStr, {
        ...existing,
        sales: existing.sales + (Number(item.sales) || 0),
        orders: existing.orders + (Number(item.orders) || 0),
        posSales: Number(item.sales) || 0,
        posOrders: Number(item.orders) || 0,
      });
    } else {
      dateMap.set(dateStr, {
        date: dateStr,
        sales: Number(item.sales) || 0,
        orders: Number(item.orders) || 0,
        onlineSales: 0,
        onlineOrders: 0,
        posSales: Number(item.sales) || 0,
        posOrders: Number(item.orders) || 0,
      });
    }
  });

  // Convert map to array and sort by date
  const result = Array.from(dateMap.values()).sort((a, b) =>
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  // Format data for CSV
  const csvData = result.map(item => ({
    Date: format(new Date(item.date), 'yyyy-MM-dd'),
    'Total Sales (AED)': item.sales.toFixed(2),
    'Total Orders': item.orders,
    'Online Sales (AED)': item.onlineSales.toFixed(2),
    'Online Orders': item.onlineOrders,
    'POS Sales (AED)': item.posSales.toFixed(2),
    'POS Orders': item.posOrders,
  }));

  // Generate CSV
  const csv = stringify(csvData, { header: true });
  res.send(csv);
}

/**
 * Export products report
 */
async function exportProductsReport(res: Response, startDate: Date, endDate: Date) {
  // Get top products from online orders
  const onlineTopProducts = await prisma.$queryRaw`
    SELECT
      p.id,
      p.name,
      p.visibility as product_visibility,
      SUM(oi.quantity) as quantity,
      SUM(oi.price * oi.quantity) as revenue
    FROM "OrderItem" oi
    JOIN "Order" o ON oi."orderId" = o.id
    JOIN "Product" p ON oi.name = p.name
    WHERE o."createdAt" >= ${startDate} AND o."createdAt" <= ${endDate}
    AND o.status = 'COMPLETED'
    GROUP BY p.id, p.name, p.visibility
  `;

  // Get top products from POS orders
  const posTopProducts = await prisma.$queryRaw`
    SELECT
      p.id,
      p.name,
      p.visibility as product_visibility,
      SUM(poi.quantity) as quantity,
      SUM(poi."unitPrice" * poi.quantity) as revenue
    FROM "POSOrderItem" poi
    JOIN "POSOrder" po ON poi."orderId" = po.id
    JOIN "Product" p ON poi."productName" = p.name
    WHERE po."createdAt" >= ${startDate} AND po."createdAt" <= ${endDate}
    AND po.status = 'COMPLETED'
    GROUP BY p.id, p.name, p.visibility
  `;

  // Combine online and POS products
  const productMap = new Map();

  // Process online products
  (onlineTopProducts as any[]).forEach(product => {
    productMap.set(product.id, {
      id: product.id,
      name: product.name,
      visibility: product.product_visibility,
      quantity: Number(product.quantity) || 0,
      revenue: Number(product.revenue) || 0,
    });
  });

  // Process POS products
  (posTopProducts as any[]).forEach(product => {
    if (productMap.has(product.id)) {
      const existing = productMap.get(product.id);
      productMap.set(product.id, {
        ...existing,
        quantity: existing.quantity + (Number(product.quantity) || 0),
        revenue: existing.revenue + (Number(product.revenue) || 0),
      });
    } else {
      productMap.set(product.id, {
        id: product.id,
        name: product.name,
        visibility: product.product_visibility,
        quantity: Number(product.quantity) || 0,
        revenue: Number(product.revenue) || 0,
      });
    }
  });

  // Convert map to array and sort by revenue
  const combinedProducts = Array.from(productMap.values())
    .sort((a, b) => b.revenue - a.revenue);

  // Calculate total revenue for percentage
  const totalRevenue = combinedProducts.reduce((sum, product) => sum + product.revenue, 0);

  // Format data for CSV
  const csvData = combinedProducts.map(product => ({
    'Product ID': product.id,
    'Product Name': product.name,
    'Visibility': product.visibility || 'ALL',
    'Quantity Sold': product.quantity,
    'Revenue (AED)': product.revenue.toFixed(2),
    'Percentage of Total': totalRevenue > 0 ? ((product.revenue / totalRevenue) * 100).toFixed(2) + '%' : '0%'
  }));

  // Generate CSV
  const csv = stringify(csvData, { header: true });
  res.send(csv);
}

/**
 * Export categories report
 */
async function exportCategoriesReport(res: Response, startDate: Date, endDate: Date) {
  // Get sales by category from online orders
  const onlineCategorySales = await prisma.$queryRaw`
    SELECT
      c.id,
      c.name as category,
      c.visibility as category_visibility,
      SUM(oi.price * oi.quantity) as sales
    FROM "OrderItem" oi
    JOIN "Order" o ON oi."orderId" = o.id
    JOIN "Product" p ON oi.name = p.name
    JOIN "Category" c ON p."categoryId" = c.id
    WHERE o."createdAt" >= ${startDate} AND o."createdAt" <= ${endDate}
    AND o.status = 'COMPLETED'
    GROUP BY c.id, c.name, c.visibility
  `;

  // Get sales by category from POS orders
  const posCategorySales = await prisma.$queryRaw`
    SELECT
      c.id,
      c.name as category,
      c.visibility as category_visibility,
      SUM(poi."unitPrice" * poi.quantity) as sales
    FROM "POSOrderItem" poi
    JOIN "POSOrder" po ON poi."orderId" = po.id
    JOIN "Product" p ON poi."productName" = p.name
    JOIN "Category" c ON p."categoryId" = c.id
    WHERE po."createdAt" >= ${startDate} AND po."createdAt" <= ${endDate}
    AND po.status = 'COMPLETED'
    GROUP BY c.id, c.name, c.visibility
  `;

  // Combine online and POS category sales
  const categoryMap = new Map();

  // Process online category sales
  (onlineCategorySales as any[]).forEach(category => {
    categoryMap.set(category.id, {
      id: category.id,
      category: category.category,
      visibility: category.category_visibility,
      sales: Number(category.sales) || 0,
    });
  });

  // Process POS category sales
  (posCategorySales as any[]).forEach(category => {
    if (categoryMap.has(category.id)) {
      const existing = categoryMap.get(category.id);
      categoryMap.set(category.id, {
        ...existing,
        sales: existing.sales + (Number(category.sales) || 0),
      });
    } else {
      categoryMap.set(category.id, {
        id: category.id,
        category: category.category,
        visibility: category.category_visibility,
        sales: Number(category.sales) || 0,
      });
    }
  });

  // Convert map to array and sort by sales
  const combinedCategories = Array.from(categoryMap.values())
    .sort((a, b) => b.sales - a.sales);

  // Calculate total sales for percentage
  const totalSales = combinedCategories.reduce((sum, cat) => sum + cat.sales, 0);

  // Format data for CSV
  const csvData = combinedCategories.map(category => ({
    'Category ID': category.id,
    'Category Name': category.category,
    'Visibility': category.visibility || 'ALL',
    'Sales (AED)': category.sales.toFixed(2),
    'Percentage of Total': totalSales > 0 ? ((category.sales / totalSales) * 100).toFixed(2) + '%' : '0%'
  }));

  // Generate CSV
  const csv = stringify(csvData, { header: true });
  res.send(csv);
}

/**
 * Export payments report
 */
async function exportPaymentsReport(res: Response, startDate: Date, endDate: Date) {
  // Get online payment method stats
  const onlinePaymentMethodStats = await prisma.$queryRaw`
    SELECT
      p."paymentMethod" as "paymentMethod",
      COUNT(DISTINCT o.id) as "orderCount",
      SUM(o.total) as "totalAmount"
    FROM "Order" o
    JOIN "Payment" p ON o.id = p."orderId"
    WHERE
      o."createdAt" >= ${startDate}
      AND o."createdAt" <= ${endDate}
      AND o.status = 'COMPLETED'
    GROUP BY p."paymentMethod"
  `;

  // Get POS payment method stats
  const posPaymentMethodStats = await prisma.$queryRaw`
    SELECT
      pp."method" as "paymentMethod",
      COUNT(DISTINCT po.id) as "orderCount",
      SUM(po.total) as "totalAmount"
    FROM "POSOrder" po
    JOIN "POSPayment" pp ON po.id = pp."orderId"
    WHERE
      po."createdAt" >= ${startDate}
      AND po."createdAt" <= ${endDate}
      AND po.status = 'COMPLETED'
    GROUP BY pp."method"
  `;

  // Combine online and POS payment stats
  const paymentMethodMap = new Map();

  // Process online payment methods
  (onlinePaymentMethodStats as any[]).forEach(item => {
    if (!item.paymentMethod) return;

    const method = String(item.paymentMethod);
    paymentMethodMap.set(method, {
      method,
      orderCount: Number(item.orderCount) || 0,
      totalAmount: parseFloat(item.totalAmount) || 0,
      source: 'Online'
    });
  });

  // Process POS payment methods
  (posPaymentMethodStats as any[]).forEach(item => {
    if (!item.paymentMethod) return;

    const method = String(item.paymentMethod);
    if (paymentMethodMap.has(method)) {
      const existing = paymentMethodMap.get(method);
      paymentMethodMap.set(method, {
        ...existing,
        orderCount: existing.orderCount + (Number(item.orderCount) || 0),
        totalAmount: existing.totalAmount + (parseFloat(item.totalAmount) || 0),
        source: 'Both'
      });
    } else {
      paymentMethodMap.set(method, {
        method,
        orderCount: Number(item.orderCount) || 0,
        totalAmount: parseFloat(item.totalAmount) || 0,
        source: 'POS'
      });
    }
  });

  // Convert map to array
  const paymentMethods = Array.from(paymentMethodMap.values());

  // Calculate total amount for percentage
  const totalAmount = paymentMethods.reduce((sum, method) => sum + method.totalAmount, 0);

  // Format data for CSV
  const csvData = paymentMethods.map(method => ({
    'Payment Method': method.method,
    'Source': method.source,
    'Order Count': method.orderCount,
    'Total Amount (AED)': method.totalAmount.toFixed(2),
    'Percentage of Total': totalAmount > 0 ? ((method.totalAmount / totalAmount) * 100).toFixed(2) + '%' : '0%'
  }));

  // Generate CSV
  const csv = stringify(csvData, { header: true });
  res.send(csv);
}

/**
 * Export orders report
 */
async function exportOrdersReport(res: Response, startDate: Date, endDate: Date) {
  // Get online orders
  const orders = await prisma.order.findMany({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    },
    include: {
      customer: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
          phone: true
        }
      },
      payment: {
        select: {
          paymentMethod: true,
          status: true,
          amount: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Format data for CSV
  const csvData = orders.map(order => {
    const subtotal = order.subtotal;
    const discount = order.discountAmount || 0;
    const discountedSubtotal = Math.max(0, subtotal - discount);
    // Calculate tax using the formula: total / 1.05 = amount before VAT, then VAT = total - amountBeforeVAT
    const amountBeforeVAT = Math.round((discountedSubtotal / 1.05) * 100) / 100;
    const tax = Math.round((discountedSubtotal - amountBeforeVAT) * 100) / 100; // VAT amount

    return {
      'Order ID': order.id,
      'Order Number': order.orderNumber || '',
      'Date': format(order.createdAt, 'yyyy-MM-dd HH:mm:ss'),
      'Customer': order.customer ? `${order.customer.firstName} ${order.customer.lastName}` : 'Guest',
      'Email': order.customer?.email || '',
      'Phone': order.customer?.phone || '',
      'Status': order.status,
      'Payment Method': order.payment?.paymentMethod || '',
      'Payment Status': order.payment?.status || order.paymentStatus,
      'Subtotal (AED)': subtotal.toFixed(2),
      'Delivery Charge (AED)': (order.deliveryCharge || 0).toFixed(2),
      'Discount (AED)': discount.toFixed(2),
      'Tax (AED)': tax.toFixed(2),
      'Total (AED)': order.total.toFixed(2),
      'Delivery Method': order.deliveryMethod || '',
      'Delivery/Pickup Date': order.deliveryDate ? format(new Date(order.deliveryDate), 'yyyy-MM-dd') : '',
      'Is Gift': order.isGift ? 'Yes' : 'No'
    };
  });

  // Generate CSV
  const csv = stringify(csvData, { header: true });
  res.send(csv);
}

/**
 * Export POS orders report
 */
async function exportPosOrdersReport(res: Response, startDate: Date, endDate: Date) {
  // Get POS orders
  const posOrders = await prisma.pOSOrder.findMany({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    },
    include: {
      createdBy: {
        select: {
          firstName: true,
          lastName: true
        }
      },
      payments: {
        orderBy: {
          createdAt: 'desc' // Get the most recent payments first
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Format data for CSV
  const csvData = posOrders.map(order => {
    // Determine the actual payment status based on the payments array
    let effectivePaymentStatus = order.paymentStatus;
    
    // If there are payments, determine the most recent valid payment status
    if (order.payments && order.payments.length > 0) {
      // Check if there's a FULLY_PAID payment (prioritize this)
      const fullyPaidPayment = order.payments.find(p => p.status === POSPaymentStatus.FULLY_PAID);
      if (fullyPaidPayment) {
        effectivePaymentStatus = POSPaymentStatus.FULLY_PAID;
      } else {
        // Check for PARTIALLY_PAID (second priority)
        const partiallyPaidPayment = order.payments.find(p => p.status === POSPaymentStatus.PARTIALLY_PAID);
        if (partiallyPaidPayment) {
          effectivePaymentStatus = POSPaymentStatus.PARTIALLY_PAID;
        }
        // If neither FULLY_PAID nor PARTIALLY_PAID exists, use PENDING (which is the default)
      }
    }
    
    // Get primary payment method (most recent payment)
    const primaryPayment = order.payments[0];

    const subtotal = order.subtotal;
    const discount = order.couponDiscount || 0;
    const discountedSubtotal = Math.max(0, subtotal - discount);
    // Calculate tax using the formula: total / 1.05 = amount before VAT, then VAT = total - amountBeforeVAT
    const amountBeforeVAT = Math.round((discountedSubtotal / 1.05) * 100) / 100;
    const tax = Math.round((discountedSubtotal - amountBeforeVAT) * 100) / 100; // VAT amount

    return {
      'Order ID': order.id,
      'Order Number': order.orderNumber,
      'Date': format(order.createdAt, 'yyyy-MM-dd HH:mm:ss'),
      'Customer Name': order.customerName || '',
      'Customer Phone': order.customerPhone || '',
      'Status': order.status,
      'Payment Status': effectivePaymentStatus,
      'Payment Method': primaryPayment?.method || '',
      'Created By': order.createdBy ? `${order.createdBy.firstName} ${order.createdBy.lastName}` : '',
      'Subtotal (AED)': subtotal.toFixed(2),
      'Delivery Charge (AED)': (order.deliveryCharge || 0).toFixed(2),
      'Discount (AED)': discount.toFixed(2),
      'Tax (AED)': tax.toFixed(2),
      'Total (AED)': order.total.toFixed(2),
      'Total Paid (AED)': order.totalPaid.toFixed(2),
      'Remaining Amount (AED)': order.remainingAmount.toFixed(2),
      'Delivery Method': order.deliveryMethod || '',
      'Emirate': order.emirate || '',
      'Is Gift': order.isGift ? 'Yes' : 'No'
    };
  });

  // Generate CSV
  const csv = stringify(csvData, { header: true });
  res.send(csv);
}

/**
 * Export purchase orders report
 */
async function exportPurchaseOrdersReport(res: Response, startDate: Date, endDate: Date) {
  // Get purchase orders
  const purchaseOrders = await prisma.purchaseOrder.findMany({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    },
    include: {
      supplier: {
        select: {
          name: true,
          email: true,
          phone: true
        }
      },
      items: {
        include: {
          item: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Format data for CSV
  const csvData = purchaseOrders.map(po => ({
    'PO ID': po.id,
    'PO Number': po.orderNumber,
    'Date': format(po.createdAt, 'yyyy-MM-dd HH:mm:ss'),
    'Supplier': po.supplier.name,
    'Supplier Email': po.supplier.email || '',
    'Supplier Phone': po.supplier.phone || '',
    'Status': po.status,
    'Payment Terms': po.paymentTerms,
    'Invoice Number': po.invoiceNumber || '',
    'Invoice Date': po.invoiceDate ? format(po.invoiceDate, 'yyyy-MM-dd') : '',
    'Subtotal': Number(po.subtotal).toFixed(2),
    'Tax': Number(po.tax).toFixed(2),
    'Additional Charge': Number(po.additionalCharge).toFixed(2),
    'Total': Number(po.total).toFixed(2),
    'Payment Reference': po.paymentReference || '',
    'Bank Payment Reference': po.bankPaymentReference || '',
    'Items Count': po.items.length,
    'Notes': po.notes || ''
  }));

  // Generate CSV
  const csv = stringify(csvData, { header: true });
  res.send(csv);
}
