import { Request, Response } from 'express';
import { prisma } from '../lib/prisma';
import { pdfStorageService, PdfUploadError } from '../lib/pdf-storage';
import multer from 'multer';
import { ApiError } from '../utils/api-error';

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only PDF files
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed'));
    }
  }
});

// Middleware to handle file upload
export const uploadInvoicePdf = upload.single('file');

// Controller to handle the upload
export const handleInvoicePdfUpload = async (req: Request, res: Response) => {
  let uploadedFileUrl: string | null = null;

  try {
    if (!req.file) {
      throw new ApiError(400, 'No file uploaded');
    }

    const file = req.file;
    const purchaseOrderId = req.body.purchaseOrderId;

    console.log(`Processing PDF upload for purchase order: ${purchaseOrderId || 'new'}, file: ${file.originalname}`);

    // Upload the file to Firebase Storage
    uploadedFileUrl = await pdfStorageService.uploadPurchaseOrderInvoice(
      purchaseOrderId || 'temp', // Use 'temp' if no purchase order ID yet
      {
        buffer: file.buffer,
        mimetype: file.mimetype,
        originalname: file.originalname
      }
    );

    console.log(`File uploaded successfully: ${uploadedFileUrl}`);

    // If we have a purchase order ID, update the record
    if (purchaseOrderId) {
      try {
        console.log(`Updating purchase order ${purchaseOrderId} with PDF URL: ${uploadedFileUrl}`);

        // Using a type assertion to handle the new field that TypeScript doesn't recognize yet
        // This is needed because we've added the field to the schema but haven't regenerated Prisma types
        const updatedOrder = await prisma.purchaseOrder.update({
          where: { id: purchaseOrderId },
          data: {
            // @ts-ignore: Field exists in the database but not in the generated types
            invoicePdfUrl: uploadedFileUrl
          }
        });

        console.log('Purchase order updated successfully:', updatedOrder.id);
      } catch (dbError) {
        console.error('Error updating purchase order with PDF URL:', dbError);

        // If database update fails, try to clean up the uploaded file
        if (uploadedFileUrl) {
          try {
            console.log('Attempting to clean up uploaded file due to database error...');
            await pdfStorageService.deletePurchaseOrderInvoice(uploadedFileUrl);
            console.log('Successfully cleaned up uploaded file');
          } catch (cleanupError) {
            console.error('Failed to clean up uploaded file:', cleanupError);
            // Don't throw here, just log the error
          }
        }

        throw new ApiError(500, 'Failed to update purchase order with PDF URL. File upload was successful but database update failed.');
      }
    }

    // Return the file URL
    return res.json({
      success: true,
      fileUrl: uploadedFileUrl,
      message: 'Invoice PDF uploaded successfully'
    });

  } catch (error) {
    console.error('Error uploading invoice PDF:', error);

    // Handle different types of errors with specific messages
    if (error instanceof PdfUploadError) {
      const statusCode = error.code === 'VALIDATION_ERROR' ? 400 : 500;
      return res.status(statusCode).json({
        success: false,
        message: error.message,
        code: error.code,
        retryable: error.retryable
      });
    }

    if (error instanceof ApiError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }

    // Generic error
    return res.status(500).json({
      success: false,
      message: 'Failed to upload invoice PDF. Please try again.',
      retryable: true
    });
  }
};
