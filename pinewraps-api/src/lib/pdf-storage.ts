import type { Bucket } from '@google-cloud/storage';
import { getBucket } from './firebase-admin';
import { nanoid } from 'nanoid';

export interface PdfUpload {
  buffer: Buffer;
  mimetype: string;
  originalname: string;
}

// Upload configuration
const UPLOAD_CONFIG = {
  MAX_RETRIES: 3,
  TIMEOUT_MS: 60000, // 60 seconds for PDF uploads
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_MIMETYPES: ['application/pdf'],
  RETRY_DELAYS: [1000, 2000, 4000] // Exponential backoff delays in ms
};

// Custom error types for better error handling
export class PdfUploadError extends Error {
  constructor(
    message: string,
    public code: string,
    public retryable: boolean = false,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'PdfUploadError';
  }
}

// Sleep utility for retry delays
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export class PdfStorageService {
  private bucket: Bucket;

  constructor() {
    this.bucket = getBucket();
  }

  /**
   * Validate PDF file before upload
   */
  private validatePdfFile(file: PdfUpload): { valid: boolean; error?: string } {
    // Check file size
    if (file.buffer.length > UPLOAD_CONFIG.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size (${Math.round(file.buffer.length / 1024 / 1024)}MB) exceeds maximum allowed size (${UPLOAD_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB)`
      };
    }

    // Check mimetype
    if (!UPLOAD_CONFIG.ALLOWED_MIMETYPES.includes(file.mimetype)) {
      return {
        valid: false,
        error: `Invalid file type. Only PDF files are allowed. Received: ${file.mimetype}`
      };
    }

    // Check if buffer is valid
    if (!file.buffer || file.buffer.length === 0) {
      return {
        valid: false,
        error: 'File is empty or corrupted'
      };
    }

    // Basic PDF header validation
    const pdfHeader = file.buffer.slice(0, 4).toString();
    if (pdfHeader !== '%PDF') {
      return {
        valid: false,
        error: 'File does not appear to be a valid PDF (missing PDF header)'
      };
    }

    return { valid: true };
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    if (!error) return false;

    const errorMessage = error.message?.toLowerCase() || '';
    const errorCode = error.code?.toLowerCase() || '';

    // Network-related errors that are typically retryable
    const retryablePatterns = [
      'timeout',
      'network',
      'connection',
      'econnreset',
      'enotfound',
      'econnrefused',
      'socket hang up',
      'request timeout',
      'service unavailable',
      'internal server error',
      'bad gateway',
      'gateway timeout'
    ];

    return retryablePatterns.some(pattern =>
      errorMessage.includes(pattern) || errorCode.includes(pattern)
    );
  }

  /**
   * Upload a PDF file to Firebase Storage with retry logic and timeout handling
   */
  async uploadPurchaseOrderInvoice(
    purchaseOrderId: string,
    file: PdfUpload
  ): Promise<string> {
    // Validate file first
    const validation = this.validatePdfFile(file);
    if (!validation.valid) {
      throw new PdfUploadError(
        validation.error!,
        'VALIDATION_ERROR',
        false
      );
    }

    console.log(`Starting PDF upload for purchase order: ${purchaseOrderId}, file: ${file.originalname}`);

    let lastError: Error | null = null;

    // Retry logic with exponential backoff
    for (let attempt = 1; attempt <= UPLOAD_CONFIG.MAX_RETRIES; attempt++) {
      try {
        console.log(`Upload attempt ${attempt}/${UPLOAD_CONFIG.MAX_RETRIES}`);

        // Generate a unique filename
        const fileExtension = file.originalname.split('.').pop()?.toLowerCase() || 'pdf';
        const filename = `${nanoid()}.${fileExtension}`;

        // Define the file path in the storage bucket
        const filePath = `purchase-orders/${purchaseOrderId}/invoices/${filename}`;

        // Create a new blob in the bucket
        const blob = this.bucket.file(filePath);

        // Upload with timeout
        const uploadPromise = blob.save(file.buffer, {
          metadata: {
            contentType: file.mimetype,
            metadata: {
              purchaseOrderId,
              originalName: file.originalname,
              uploadAttempt: attempt.toString(),
              uploadTimestamp: new Date().toISOString()
            }
          }
        });

        // Create timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new PdfUploadError(
              `Upload timed out after ${UPLOAD_CONFIG.TIMEOUT_MS / 1000} seconds`,
              'TIMEOUT_ERROR',
              true
            ));
          }, UPLOAD_CONFIG.TIMEOUT_MS);
        });

        // Race between upload and timeout
        await Promise.race([uploadPromise, timeoutPromise]);

        console.log(`File uploaded successfully, making public...`);

        // Make the file publicly accessible with timeout
        const makePublicPromise = blob.makePublic();
        const makePublicTimeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new PdfUploadError(
              'Making file public timed out',
              'MAKE_PUBLIC_TIMEOUT',
              true
            ));
          }, 10000); // 10 second timeout for making public
        });

        await Promise.race([makePublicPromise, makePublicTimeoutPromise]);

        // Get the public URL
        const publicUrl = `https://storage.googleapis.com/${this.bucket.name}/${filePath}`;

        console.log(`PDF upload successful: ${publicUrl}`);
        return publicUrl;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.warn(`Upload attempt ${attempt} failed:`, lastError.message);

        // If this is the last attempt or error is not retryable, don't wait
        if (attempt === UPLOAD_CONFIG.MAX_RETRIES || !this.isRetryableError(lastError)) {
          break;
        }

        // Wait before retry with exponential backoff
        const delayMs = UPLOAD_CONFIG.RETRY_DELAYS[attempt - 1] || 4000;
        console.log(`Waiting ${delayMs}ms before retry...`);
        await sleep(delayMs);
      }
    }

    // All attempts failed
    console.error(`All ${UPLOAD_CONFIG.MAX_RETRIES} upload attempts failed:`, lastError?.message);

    if (lastError instanceof PdfUploadError) {
      throw lastError;
    }

    throw new PdfUploadError(
      `Failed to upload PDF after ${UPLOAD_CONFIG.MAX_RETRIES} attempts: ${lastError?.message}`,
      'UPLOAD_FAILED',
      this.isRetryableError(lastError)
    );
  }

  /**
   * Delete a PDF file from Firebase Storage
   */
  async deletePurchaseOrderInvoice(fileUrl: string): Promise<void> {
    try {
      // Extract the file path from the URL
      const urlParts = fileUrl.split(`https://storage.googleapis.com/${this.bucket.name}/`);
      
      if (urlParts.length !== 2) {
        throw new Error('Invalid file URL format');
      }
      
      const filePath = urlParts[1];
      
      // Get the file reference
      const file = this.bucket.file(filePath);
      
      // Check if file exists
      const [exists] = await file.exists();
      
      if (!exists) {
        console.warn(`File ${filePath} does not exist in storage`);
        return;
      }
      
      // Delete the file
      await file.delete();
      
      console.log(`Successfully deleted file: ${filePath}`);
    } catch (error) {
      console.error('Error deleting PDF:', error);
      throw new Error(`Failed to delete PDF: ${error.message}`);
    }
  }
}

// Export a singleton instance
export const pdfStorageService = new PdfStorageService();
