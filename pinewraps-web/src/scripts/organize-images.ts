import { storage } from '@/lib/firebase';
import { ref, listAll, getDownloadURL, getMetadata, uploadBytes } from 'firebase/storage';
import sharp from 'sharp';
import fetch from 'node-fetch';

async function downloadAndOptimizeImage(url: string): Promise<Buffer> {
  const response = await fetch(url);
  const buffer = Buffer.from(await response.arrayBuffer());
  
  return sharp(buffer)
    .webp({ quality: 80 })
    .toBuffer();
}

async function moveToPublic() {
  try {
    // List all files in storage
    const rootRef = ref(storage);
    const result = await listAll(rootRef);
    
    // Process each file
    for (const item of result.items) {
      try {
        const metadata = await getMetadata(item);
        
        // Skip if not an image or already in public folder
        if (!metadata.contentType?.startsWith('image/') || item.fullPath.startsWith('public/')) {
          continue;
        }
        
        // Get download URL
        const url = await getDownloadURL(item);
        
        // Download and optimize image
        const optimizedBuffer = await downloadAndOptimizeImage(url);
        
        // Upload to public folder
        const publicPath = `public/${item.fullPath}`;
        const publicRef = ref(storage, publicPath);
        
        await uploadBytes(publicRef, optimizedBuffer, {
          contentType: 'image/webp',
          customMetadata: {
            originalPath: item.fullPath,
            optimized: 'true'
          }
        });
        
        console.log(`✓ Moved and optimized: ${item.fullPath} -> ${publicPath}`);
      } catch (error) {
        console.error(`Error processing ${item.fullPath}:`, error);
      }
    }
    
    console.log('Image organization complete!');
  } catch (error) {
    console.error('Error organizing images:', error);
  }
}

// Run the script
moveToPublic();
