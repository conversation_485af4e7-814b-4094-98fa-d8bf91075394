'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Product } from '@/services/api';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ProductFiltersProps {
  products: Product[];
  onFiltersChange: (filters: FilterState) => void;
}

interface FilterState {
  category: string;
  sort: string;
}

export default function ProductFilters({ products, onFiltersChange }: ProductFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [filters, setFilters] = useState<FilterState>({
    category: searchParams.get('category') || 'all',
    sort: searchParams.get('sort') || 'featured'
  });

  // Get unique categories
  const categories = ['all', ...new Set(products.map(p => p.category?.name).filter(Boolean))];

  const sortOptions = [
    { value: 'featured', label: 'Featured' },
    { value: 'price-asc', label: 'Price: Low to High' },
    { value: 'price-desc', label: 'Price: High to Low' },
    { value: 'newest', label: 'Newest First' }
  ];

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // Update URL without navigation
    const params = new URLSearchParams(searchParams.toString());
    params.set(key, value);
    router.replace(`/shop?${params.toString()}`, { scroll: false });
    
    // Notify parent component
    onFiltersChange(newFilters);
  };

  useEffect(() => {
    // Initial filter notification
    onFiltersChange(filters);
  }, []);

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
      <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
        <div className="flex-1 sm:flex-none">
          <Select
            value={filters.category}
            onValueChange={(value) => handleFilterChange('category', value)}
          >
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Select Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex-1 sm:flex-none">
          <Select
            value={filters.sort}
            onValueChange={(value) => handleFilterChange('sort', value)}
          >
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="text-sm text-gray-600 w-full sm:w-auto text-center sm:text-right">
        Showing {products.length} products
      </div>
    </div>
  );
}
