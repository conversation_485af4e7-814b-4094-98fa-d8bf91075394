'use client';

import { Suspense, useState, useEffect } from 'react';
import { useScrollSubcategories } from '@/hooks/use-scroll-subcategories';

interface ScrollSubcategoryFiltersProps {
  categoryName?: string; // For specific category pages like 'Cakes', 'Flowers', 'Sets'
  selectedCategory?: string; // For shop page with dynamic category selection
  className?: string;
}

// Loading skeleton component
function ScrollSubcategoryFiltersSkeleton() {
  return (
    <div className="flex gap-1 sm:gap-2 items-center animate-pulse overflow-x-auto scrollbar-hide">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-gray-100 rounded-full h-8 sm:h-10 w-16 sm:w-20 flex-shrink-0"></div>
      ))}
    </div>
  );
}

// Error component
function ScrollSubcategoryFiltersError({ error }: { error: string }) {
  return (
    <div className="text-sm text-red-600">
      {error}
    </div>
  );
}

// Main filters component (wrapped in Suspense)
function ScrollSubcategoryFiltersContent({
  categoryName,
  selectedCategory,
  className = ''
}: ScrollSubcategoryFiltersProps) {
  const {
    subcategories,
    isLoading,
    error,
    hasSubcategories
  } = useScrollSubcategories({ categoryName, selectedCategory });

  // Track active section
  const [activeSection, setActiveSection] = useState<string>('all-products');

  // Function to scroll to section and update active state
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      setActiveSection(sectionId);
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Intersection Observer to track which section is currently visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '-20% 0px -60% 0px', // Trigger when section is 20% from top
      threshold: 0
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setActiveSection(entry.target.id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    // Observe all sections
    const allProductsSection = document.getElementById('all-products');
    if (allProductsSection) {
      observer.observe(allProductsSection);
    }

    subcategories.forEach((subcategory) => {
      const section = document.getElementById(`section-${subcategory.id}`);
      if (section) {
        observer.observe(section);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [subcategories]);

  // Don't render if no subcategories and not loading
  if (!hasSubcategories && !isLoading && !error) {
    return null;
  }

  // Show error state
  if (error) {
    return <ScrollSubcategoryFiltersError error={error} />;
  }

  // Show loading state
  if (isLoading) {
    return <ScrollSubcategoryFiltersSkeleton />;
  }

  return (
    <div className={`flex gap-1 sm:gap-2 items-center overflow-x-auto scrollbar-hide pb-1 sm:pb-2 ${className}`}>
      <button
        onClick={() => scrollToSection('all-products')}
        className={`px-3 py-2 sm:px-4 sm:py-2.5 rounded-full text-xs sm:text-sm font-medium transition-all duration-200 shadow-md flex-shrink-0 ${
          activeSection === 'all-products'
            ? 'bg-gray-800 text-white hover:bg-gray-700'
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
        }`}
      >
        All
      </button>
      {subcategories.map((subcategory) => (
        <button
          key={subcategory.id}
          onClick={() => scrollToSection(`section-${subcategory.id}`)}
          className={`px-3 py-2 sm:px-4 sm:py-2.5 rounded-full text-xs sm:text-sm font-medium transition-all duration-200 flex-shrink-0 whitespace-nowrap ${
            activeSection === `section-${subcategory.id}`
              ? 'bg-gray-800 text-white hover:bg-gray-700 shadow-md'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
          }`}
        >
          {subcategory.name}
        </button>
      ))}
    </div>
  );
}

// Main exported component with Suspense boundary
export function ScrollSubcategoryFilters(props: ScrollSubcategoryFiltersProps) {
  return (
    <Suspense fallback={<ScrollSubcategoryFiltersSkeleton />}>
      <ScrollSubcategoryFiltersContent {...props} />
    </Suspense>
  );
}

export default ScrollSubcategoryFilters;
