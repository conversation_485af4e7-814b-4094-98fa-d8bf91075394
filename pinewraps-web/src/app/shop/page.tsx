'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import PageTitle from "@/components/ui/page-title";
import Link from "next/link";
import { getProducts, formatPrice, Category, SortOption } from "@/services/api";
import { Product } from "@/types/product";
import ProductGridImage from "@/components/shop/product-grid-image";
import ErrorMessage from "@/components/shop/error-message";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollSubcategoryFilters } from "@/components/filters/scroll-subcategory-filters";
import { useScrollSubcategories } from '@/hooks/use-scroll-subcategories';
import { Package } from "lucide-react";

const sortOptions: { value: SortOption; label: string }[] = [
  { value: 'createdAt:desc', label: 'Newest First' },
  { value: 'createdAt:asc', label: 'Oldest First' },
  { value: 'basePrice:desc', label: 'Price: High to Low' },
  { value: 'basePrice:asc', label: 'Price: Low to High' },
];

function getProductPrice(product: Product): { min: number; max: number | null } {
  // Special handling for Sets category
  if (product.category?.name === 'Sets') {
    // For Sets, we want to show the final price (base + all four options combined)
    // Find the highest price variants for the 4 cake options
    let totalPrice = product.basePrice;

    // Get unique option types for cake flavors
    const cakeFlavorOptions = product.variants
      .filter(variant =>
        variant.values.some(val =>
          val.value.option.name.toLowerCase().includes('cake') ||
          val.value.option.name.toLowerCase().includes('flavor')
        )
      );

    // Get the 4 highest priced variants (assuming 4 cakes in a set)
    const highestPriceVariants = cakeFlavorOptions
      .sort((a, b) => b.price - a.price)
      .slice(0, 4);

    // Add the prices of the highest variants to the base price
    highestPriceVariants.forEach(variant => {
      totalPrice += variant.price;
    });

    return { min: totalPrice, max: null };
  }

  // For other categories, use the existing logic
  // If no variants, return base price
  if (!product.variants || product.variants.length === 0) {
    return { min: product.basePrice, max: null };
  }

  const allPrices: number[] = [];

  // Get all variant prices
  product.variants.forEach(variant => {
    if (typeof variant.price === 'number') {
      allPrices.push(variant.price);
    }
  });

  // If no prices found, return base price
  if (allPrices.length === 0) {
    return { min: product.basePrice, max: null };
  }

  // Return min and max prices
  const min = Math.min(...allPrices);
  const max = Math.max(...allPrices);

  return {
    min,
    max: min !== max ? max : null
  };
}

// Helper function to check if product has ready stock (variants or base product)
function hasReadyStockVariants(product: Product): boolean {
  // Check if base product is ready stock (for products without variations)
  if (product.isReadyStock === true) {
    return true;
  }
  // Check if any variants are ready stock (for products with variations)
  return product.variants?.some(variant => variant.isReadyStock === true) || false;
}

// Helper function to get ready stock count
function getReadyStockCount(product: Product): number {
  let totalCount = 0;

  // Add base product ready stock quantity (for products without variations)
  if (product.isReadyStock === true) {
    totalCount += product.readyStockQty || 0;
  }

  // Add variant ready stock quantities (for products with variations)
  if (product.variants) {
    totalCount += product.variants
      .filter(variant => variant.isReadyStock === true)
      .reduce((total, variant) => total + (variant.readyStockQty || 0), 0);
  }

  return totalCount;
}

// Fallback component for Suspense boundary
function ShopPageFallback() {
  return (
    <>
      <PageTitle
        title="Shop"
        breadcrumbs={[
          { label: "Home", href: "/" },
          { label: "Shop", href: "/shop" }
        ]}
      />
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Filters skeleton */}
          <div className="mb-8 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-wrap gap-4 items-center animate-pulse">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-gray-100 rounded-md h-10 w-24"></div>
              ))}
            </div>
            <div className="bg-gray-100 rounded-md h-10 w-[200px] animate-pulse"></div>
          </div>

          {/* Products skeleton */}
          <div className="animate-pulse">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(12)].map((_, i) => (
                <div key={i} className="rounded-lg overflow-hidden border border-gray-100">
                  <div className="bg-gray-100 h-64 w-full"></div>
                  <div className="p-4">
                    <div className="bg-gray-100 h-6 w-3/4 mb-2 rounded"></div>
                    <div className="bg-gray-100 h-4 w-1/2 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Main shop page content component
function ShopPageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Initialize state from URL parameters or defaults
  const [selectedCategory, setSelectedCategory] = useState<string>(() => {
    return searchParams.get('category') || '';
  });

  const [selectedSort, setSelectedSort] = useState<SortOption>(() => {
    return (searchParams.get('sort') as SortOption) || 'createdAt:desc';
  });

  // Use the scroll subcategories hook to get subcategories for the selected category
  const { subcategories } = useScrollSubcategories({ selectedCategory });

  // Group products by subcategory
  const groupedProducts = subcategories.reduce((acc, subcategory) => {
    acc[subcategory.id] = products.filter(product => product.subcategoryId === subcategory.id);
    return acc;
  }, {} as Record<string, Product[]>);

  // Add canonical tag for SEO
  useEffect(() => {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://pinewraps.com';
    const canonicalUrl = `${baseUrl}/shop`;

    // Remove existing canonical tag if any
    const existingCanonical = document.querySelector('link[rel="canonical"]');
    if (existingCanonical) {
      existingCanonical.remove();
    }

    // Add new canonical tag
    const canonical = document.createElement('link');
    canonical.rel = 'canonical';
    canonical.href = canonicalUrl;
    document.head.appendChild(canonical);

    // Cleanup function to remove canonical tag when component unmounts
    return () => {
      const canonicalToRemove = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      if (canonicalToRemove && canonicalToRemove.href === canonicalUrl) {
        canonicalToRemove.remove();
      }
    };
  }, []);

  // Helper function to create query strings
  const createQueryString = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value) {
      params.set(name, value);
    } else {
      params.delete(name);
    }
    return params.toString();
  };

  // Update URL when state changes
  useEffect(() => {
    const params = new URLSearchParams();

    if (selectedCategory) {
      params.set('category', selectedCategory);
    }

    if (selectedSort !== 'createdAt:desc') {
      params.set('sort', selectedSort);
    }

    const newUrl = `/shop${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newUrl, { scroll: false });
  }, [selectedCategory, selectedSort, router]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // Use hierarchical endpoint to get proper parent-child structure
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/categories/public/hierarchical?platform=WEB`,
          {
            headers: {
              'X-Platform': 'WEB'
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.success && Array.isArray(data.data)) {
            // Extract only parent categories (those returned by hierarchical endpoint are already parents)
            setCategories(data.data);
          } else {
            console.error('Invalid categories data format');
            setCategories([]);
          }
        } else {
          throw new Error('Failed to fetch categories');
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        setCategories([]);
      }
    };

    fetchCategories();
  }, []);

  // Simple memoization for product fetching
  const memoizedProductFetch = (() => {
    const cache: Record<string, { data: any, timestamp: number }> = {};

    return async (page: number, limit: number, category?: string, sort?: SortOption, subcategory?: string) => {
      const cacheKey = `${page}-${limit}-${category || ''}-${sort || ''}-${subcategory || ''}`;
      const now = Date.now();
      const cacheTime = 5 * 60 * 1000; // 5 minutes

      // Return cached result if valid
      if (cache[cacheKey] && now - cache[cacheKey].timestamp < cacheTime) {
        return cache[cacheKey].data;
      }

      // Fetch new data
      const result = await getProducts(page, limit, category, sort, subcategory);

      // Update cache
      cache[cacheKey] = { data: result, timestamp: now };

      return result;
    };
  })();

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // Use the memoized function to fetch products (without pagination)
        const result = await memoizedProductFetch(
          1, // page
          1000, // high limit to get all products
          selectedCategory || undefined,
          selectedSort,
          undefined // no subcategory filtering since we'll handle it with scroll sections
        );

        const productsData = result?.data || [];

        // Map API products to match the Product type expected by ProductGridImage
        const mappedProducts = productsData.map((product: any) => ({
          ...product,
          // Add missing properties required by our Product type
          stock: product.stock ?? 0,
          options: product.options ?? [],
          updatedAt: product.updatedAt ?? product.createdAt
        })) as Product[];

        setProducts(mappedProducts);

        // Store in sessionStorage for faster access on back navigation
        sessionStorage.setItem('shop_products', JSON.stringify({
          products: mappedProducts,
          timestamp: Date.now(),
          params: { category: selectedCategory, sort: selectedSort }
        }));
      } catch (error) {
        console.error('Error fetching products:', error);
        setError('Failed to load products. Please try again later.');
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    // Check if we have cached data in sessionStorage that matches current params
    const cachedData = sessionStorage.getItem('shop_products');
    if (cachedData) {
      try {
        const parsed = JSON.parse(cachedData);
        const params = parsed.params;
        const isParamsMatch =
          params.category === selectedCategory &&
          params.sort === selectedSort;

        // Use cached data if parameters match and cache is less than 5 minutes old
        const isCacheValid = Date.now() - parsed.timestamp < 5 * 60 * 1000; // 5 minutes

        if (isParamsMatch && isCacheValid) {
          setProducts(parsed.products);
          setLoading(false);
          return;
        }
      } catch (e) {
        console.error('Error parsing cached products:', e);
        // Continue with fetching if there's an error with the cache
      }
    }

    fetchProducts();
  }, [selectedCategory, selectedSort]);

  const handleCategoryChange = (categoryId: string) => {
    const queryString = createQueryString('category', categoryId);
    const newUrl = `/shop${queryString ? `?${queryString}` : ''}`;
    router.push(newUrl, { scroll: false });
    setSelectedCategory(categoryId);
  };

  const handleSortChange = (sort: SortOption) => {
    const queryString = createQueryString('sort', sort);
    const newUrl = `/shop${queryString ? `?${queryString}` : ''}`;
    router.push(newUrl, { scroll: false });
    setSelectedSort(sort);
  };

  if (loading) {
    return (
      <>
        <PageTitle
          title="Shop"
          breadcrumbs={[
            { label: "Home", href: "/" },
            { label: "Shop", href: "/shop" }
          ]}
        />
        <div className="min-h-screen bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Filters skeleton */}
            <div className="mb-8 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-wrap gap-4 items-center animate-pulse">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="bg-gray-100 rounded-md h-10 w-24"></div>
                ))}
              </div>
              <div className="bg-gray-100 rounded-md h-10 w-[200px] animate-pulse"></div>
            </div>

            {/* Products skeleton - white background */}
            <div className="animate-pulse">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[...Array(12)].map((_, i) => (
                  <div key={i} className="rounded-lg overflow-hidden border border-gray-100">
                    <div className="bg-gray-100 h-64 w-full"></div>
                    <div className="p-4">
                      <div className="bg-gray-100 h-6 w-3/4 mb-2 rounded"></div>
                      <div className="bg-gray-100 h-4 w-1/2 rounded"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageTitle
          title="Shop"
          breadcrumbs={[
            { label: "Home", href: "/" },
            { label: "Shop", href: "/shop" }
          ]}
        />
        <div className="min-h-screen bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ErrorMessage message={error} />
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <PageTitle
        title="Shop"
        description={`${products.length} products available`}
        breadcrumbs={[
          { label: "Home", href: "/" },
          { label: "Shop", href: "/shop" }
        ]}
      />
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Filters */}
          <div className="mb-4">
            {/* Category Filters */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-wrap gap-4 items-center">
                <Button
                  variant={selectedCategory === '' ? 'default' : 'outline'}
                  onClick={() => handleCategoryChange('')}
                  className="flex items-center gap-2"
                >
                  All Products
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'default' : 'outline'}
                    onClick={() => handleCategoryChange(category.id)}
                    className="flex items-center gap-2"
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
              <Select value={selectedSort} onValueChange={(value) => handleSortChange(value as SortOption)}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Subcategory Navigation - Sticky (outside main container) */}
        {selectedCategory && (
          <div className="sticky top-14 md:top-16 z-20 bg-white border-b border-gray-200 shadow-sm">
            <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-2 sm:py-3">
              <Suspense fallback={<div className="flex gap-1 sm:gap-2 items-center animate-pulse overflow-x-auto scrollbar-hide">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="bg-gray-100 rounded-full h-8 sm:h-10 w-16 sm:w-20 flex-shrink-0"></div>
                ))}
              </div>}>
                <ScrollSubcategoryFilters selectedCategory={selectedCategory} />
              </Suspense>
            </div>
          </div>
        )}

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8 pt-8">

          {(!products || products.length === 0) ? (
            <div className="text-center py-8">
              <h3 className="text-lg font-medium">No products found</h3>
              <p className="text-gray-600 mt-2">Try adjusting your filters or check back later for new products</p>
            </div>
          ) : (
            <>
              {(() => {
                // Show scroll-based sections when a specific category is selected
                if (selectedCategory && subcategories.length > 0) {
                  return (
                    <div className="space-y-16">
                      {/* All Products Section */}
                      <section id="all-products" className="scroll-mt-36 md:scroll-mt-40">
                        <h2 className="text-2xl font-semibold text-gray-900 mb-8">All Products</h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                          {products.map((product) => {
                            const price = getProductPrice(product);
                            const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];
                            const hasReadyStock = hasReadyStockVariants(product);
                            const readyStockCount = getReadyStockCount(product);

                            return (
                              <Link
                                key={product.id}
                                href={`/shop/${product.slug}`}
                                className="group"
                              >
                                <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform duration-200 ease-in-out group-hover:shadow-md">
                                  <div className="relative aspect-square">
                                    <ProductGridImage
                                      product={product}
                                      src={primaryImage?.url || '/placeholder.jpg'}
                                      alt={primaryImage?.alt || product.name}
                                      className="w-full h-full object-cover"
                                    />
                                    {/* Ready Stock Badge */}
                                    {hasReadyStock && (
                                      <div className="absolute top-2 left-2">
                                        <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 text-xs">
                                          <Package className="w-3 h-3 mr-1" />
                                          {readyStockCount > 0 ? `${readyStockCount} Ready` : 'Ready'}
                                        </Badge>
                                      </div>
                                    )}
                                  </div>
                                  <div className="p-4">
                                    <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                      {product.name}
                                    </h3>
                                    <p className="mt-2 text-gray-600">
                                      {product.category?.name === 'Sets'
                                        ? `Starting from 332 AED`
                                        : price.max
                                          ? `${formatPrice(price.min)} - ${formatPrice(price.max)}`
                                          : formatPrice(price.min)
                                      }
                                    </p>
                                  </div>
                                </div>
                              </Link>
                            );
                          })}
                        </div>
                      </section>

                      {/* Subcategory Sections */}
                      {subcategories.map((subcategory) => {
                        const subcategoryProducts = groupedProducts[subcategory.id] || [];

                        if (subcategoryProducts.length === 0) return null;

                        return (
                          <section key={subcategory.id} id={`section-${subcategory.id}`} className="scroll-mt-36 md:scroll-mt-40">
                            <h2 className="text-2xl font-semibold text-gray-900 mb-8">{subcategory.name}</h2>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                              {subcategoryProducts.map((product) => {
                                const price = getProductPrice(product);
                                const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];
                                const hasReadyStock = hasReadyStockVariants(product);
                                const readyStockCount = getReadyStockCount(product);

                                return (
                                  <Link
                                    key={product.id}
                                    href={`/shop/${product.slug}`}
                                    className="group"
                                  >
                                    <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform duration-200 ease-in-out group-hover:shadow-md">
                                      <div className="relative aspect-square">
                                        <ProductGridImage
                                          product={product}
                                          src={primaryImage?.url || '/placeholder.jpg'}
                                          alt={primaryImage?.alt || product.name}
                                          className="w-full h-full object-cover"
                                        />
                                        {/* Ready Stock Badge */}
                                        {hasReadyStock && (
                                          <div className="absolute top-2 left-2">
                                            <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 text-xs">
                                              <Package className="w-3 h-3 mr-1" />
                                              {readyStockCount > 0 ? `${readyStockCount} Ready` : 'Ready'}
                                            </Badge>
                                          </div>
                                        )}
                                      </div>
                                      <div className="p-4">
                                        <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                          {product.name}
                                        </h3>
                                        <p className="mt-2 text-gray-600">
                                          {product.category?.name === 'Sets'
                                            ? `Starting from 332 AED`
                                            : price.max
                                              ? `${formatPrice(price.min)} - ${formatPrice(price.max)}`
                                              : formatPrice(price.min)
                                          }
                                        </p>
                                      </div>
                                    </div>
                                  </Link>
                                );
                              })}
                            </div>
                          </section>
                        );
                      })}
                    </div>
                  );
                }
                
                // Fallback to regular grid display if no category selected or no subcategories
                return (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                    {products.map((product) => {
                      const price = getProductPrice(product);
                      const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];
                      const hasReadyStock = hasReadyStockVariants(product);
                      const readyStockCount = getReadyStockCount(product);

                      return (
                        <Link
                          key={product.id}
                          href={`/shop/${product.slug}`}
                          className="group"
                        >
                          <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform duration-200 ease-in-out group-hover:shadow-md">
                            <div className="relative aspect-square">
                              <ProductGridImage
                                product={product}
                                src={primaryImage?.url || '/placeholder.jpg'}
                                alt={primaryImage?.alt || product.name}
                                className="w-full h-full object-cover"
                              />
                              {/* Ready Stock Badge */}
                              {hasReadyStock && (
                                <div className="absolute top-2 left-2">
                                  <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 text-xs">
                                    <Package className="w-3 h-3 mr-1" />
                                    {readyStockCount > 0 ? `${readyStockCount} Ready` : 'Ready'}
                                  </Badge>
                                </div>
                              )}
                            </div>
                            <div className="p-4">
                              <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                {product.name}
                              </h3>
                              <p className="mt-2 text-gray-600">
                                {product.category?.name === 'Sets'
                                  ? `Starting from 332 AED`
                                  : price.max
                                    ? `${formatPrice(price.min)} - ${formatPrice(price.max)}`
                                    : formatPrice(price.min)
                                }
                              </p>
                            </div>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                );
              })()}
            </>
          )}
        </div>
      </div>
    </>
  );
}

// Main exported component with Suspense boundary
export default function ShopPage() {
  return (
    <Suspense fallback={<ShopPageFallback />}>
      <ShopPageContent />
    </Suspense>
  );
}