'use client';

import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import PageTitle from '@/components/ui/page-title';
import { Product } from '@/types/product';
import { getProductPrice } from '@/utils/product';
import { formatPrice } from '@/utils/format';
import ProductGridImage from '@/components/shop/product-grid-image';
import ErrorMessage from '@/components/shop/error-message';
import { ScrollSubcategoryFilters } from '@/components/filters/scroll-subcategory-filters';
import { useScrollSubcategories } from '@/hooks/use-scroll-subcategories';

// Fallback component for Suspense boundary
function CakesPageFallback() {
  return (
    <>
      <PageTitle
        title="Luxury Cakes Collection"
        description="Handcrafted with love and premium ingredients"
        breadcrumbs={[
          { label: "Home", href: "/" },
          { label: "Shop", href: "/shop" },
          { label: "Cakes", href: "/cakes" }
        ]}
      />
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-gray-200 rounded-lg h-64"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Main content component
function CakesPageContent() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use the subcategory filters hook to get subcategories
  const { subcategories } = useScrollSubcategories({ categoryName: 'Cakes' });

  // Group products by subcategory
  const groupedProducts = subcategories.reduce((acc, subcategory) => {
    acc[subcategory.id] = products.filter(product => product.subcategoryId === subcategory.id);
    return acc;
  }, {} as Record<string, Product[]>);

  // Add canonical tag for SEO
  useEffect(() => {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://pinewraps.com';
    const canonicalUrl = `${baseUrl}/cakes`;

    // Remove existing canonical tag if any
    const existingCanonical = document.querySelector('link[rel="canonical"]');
    if (existingCanonical) {
      existingCanonical.remove();
    }

    // Add new canonical tag
    const canonical = document.createElement('link');
    canonical.rel = 'canonical';
    canonical.href = canonicalUrl;
    document.head.appendChild(canonical);

    // Cleanup function to remove canonical tag when component unmounts
    return () => {
      const canonicalToRemove = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      if (canonicalToRemove && canonicalToRemove.href === canonicalUrl) {
        canonicalToRemove.remove();
      }
    };
  }, []);



  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // First, get the category ID for Cakes
        const categoryRes = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/categories/public`
        );
        
        if (!categoryRes.ok) {
          throw new Error('Failed to fetch categories');
        }

        const categoryData = await categoryRes.json();
        const cakesCategory = categoryData.data.find((cat: any) => cat.name === 'Cakes');

        if (!cakesCategory) {
          throw new Error('Cakes category not found');
        }

        // Build the products URL to fetch all products (high limit)
        let productsUrl = `${process.env.NEXT_PUBLIC_API_URL}/api/products/public?category=${cakesCategory.id}&limit=1000`;

        // Fetch all products for this category
        const productsRes = await fetch(productsUrl);
        
        if (!productsRes.ok) {
          throw new Error('Failed to fetch products');
        }

        const result = await productsRes.json();
        
        if (!result.success) {
          throw new Error(result.data);
        }

        const { products } = result.data;

        // Map API products to our Product type
        const mappedProducts = products.map((product: any) => ({
          ...product,
          // Add missing properties from our Product type
          stock: product.stock ?? 0,
          options: product.options ?? [],
          updatedAt: product.updatedAt ?? product.createdAt,
          // Keep variant and variation data
          variants: product.variants ?? [],
          variations: product.variations ?? [],
          variantCombinations: product.variantCombinations ?? []
        })) as Product[];

        setProducts(mappedProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
        setError('Failed to load products. Please try again later.');
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);



  if (loading) {
    return (
      <>
        <PageTitle 
          title="Luxury Cakes Collection" 
          description="Handcrafted with love and premium ingredients"
          breadcrumbs={[
            { label: "Home", href: "/" },
            { label: "Shop", href: "/shop" },
            { label: "Cakes", href: "/cakes" }
          ]}
        />
        <div className="min-h-screen bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="animate-pulse">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="bg-gray-200 rounded-lg h-64"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageTitle 
          title="Luxury Cakes Collection" 
          description="Handcrafted with love and premium ingredients"
          breadcrumbs={[
            { label: "Home", href: "/" },
            { label: "Shop", href: "/shop" },
            { label: "Cakes", href: "/cakes" }
          ]}
        />
        <div className="min-h-screen bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ErrorMessage message={error} />
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <PageTitle 
        title="Luxury Cakes Collection" 
        breadcrumbs={[
          { label: "Home", href: "/" },
          { label: "Shop", href: "/shop" },
          { label: "Cakes", href: "/cakes" }
        ]}
      />

      <div className="min-h-screen bg-white">
        {/* Category Description for SEO */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="prose max-w-none">
            <p className="text-gray-600 text-lg leading-relaxed">
              Welcome to Pinewraps&apos; exclusive cake collection. Each cake in our collection is a masterpiece, 
              handcrafted with premium ingredients and meticulous attention to detail. Whether you&apos;re celebrating 
              a birthday, wedding, or any special occasion, our luxury cakes are designed to make your moment 
              unforgettable. From classic flavors to innovative creations, discover the perfect cake that matches 
              your taste and style.
            </p>
          </div>
        </div>

        {/* Subcategory Navigation - Sticky */}
        <div className="sticky top-14 md:top-16 z-20 bg-white border-b border-gray-200 shadow-sm">
          <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-2 sm:py-3">
            <Suspense fallback={<div className="flex gap-1 sm:gap-2 items-center animate-pulse overflow-x-auto scrollbar-hide">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-gray-100 rounded-full h-8 sm:h-10 w-16 sm:w-20 flex-shrink-0"></div>
              ))}
            </div>}>
              <ScrollSubcategoryFilters categoryName="Cakes" />
            </Suspense>
          </div>
        </div>

        {/* Products Sections */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {(!products || products.length === 0) ? (
            <div className="text-center py-8">
              <h3 className="text-lg font-medium">No cakes available</h3>
              <p className="text-gray-600 mt-2">Check back later for new products</p>
            </div>
          ) : (
            <div className="space-y-16">
              {/* All Products Section */}
              <section id="all-products" className="scroll-mt-36 md:scroll-mt-40">
                <h2 className="text-2xl font-semibold text-gray-900 mb-8">All Cakes</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {products.map((product) => {
                    const price = getProductPrice(product);
                    const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];

                    return (
                      <Link
                        key={product.id}
                        href={`/shop/${product.slug}`}
                        className="group"
                      >
                        <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform duration-200 ease-in-out group-hover:shadow-md">
                          <div className="relative aspect-square">
                            <ProductGridImage
                              product={product}
                              src={primaryImage?.url || '/placeholder.jpg'}
                              alt={primaryImage?.alt || product.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="p-4">
                            <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                              {product.name}
                            </h3>
                            <p className="mt-2 text-gray-600">
                              {price.max
                                ? `${formatPrice(price.min)} - ${formatPrice(price.max)}`
                                : formatPrice(price.min)}
                            </p>
                          </div>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              </section>

              {/* Subcategory Sections */}
              {subcategories.map((subcategory) => {
                const subcategoryProducts = groupedProducts[subcategory.id] || [];

                if (subcategoryProducts.length === 0) return null;

                return (
                  <section key={subcategory.id} id={`section-${subcategory.id}`} className="scroll-mt-36 md:scroll-mt-40">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-8">{subcategory.name}</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {subcategoryProducts.map((product) => {
                        const price = getProductPrice(product);
                        const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];

                        return (
                          <Link
                            key={product.id}
                            href={`/shop/${product.slug}`}
                            className="group"
                          >
                            <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform duration-200 ease-in-out group-hover:shadow-md">
                              <div className="relative aspect-square">
                                <ProductGridImage
                                  product={product}
                                  src={primaryImage?.url || '/placeholder.jpg'}
                                  alt={primaryImage?.alt || product.name}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <div className="p-4">
                                <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                  {product.name}
                                </h3>
                                <p className="mt-2 text-gray-600">
                                  {price.max
                                    ? `${formatPrice(price.min)} - ${formatPrice(price.max)}`
                                    : formatPrice(price.min)}
                                </p>
                              </div>
                            </div>
                          </Link>
                        );
                      })}
                    </div>
                  </section>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </>
  );
}

// Main exported component with Suspense boundary
export default function CakesPage() {
  return (
    <Suspense fallback={<CakesPageFallback />}>
      <CakesPageContent />
    </Suspense>
  );
}