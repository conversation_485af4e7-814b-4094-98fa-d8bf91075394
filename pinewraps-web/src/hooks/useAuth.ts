import { useState, useEffect } from 'react';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '@/lib/firebase';

interface CustomerData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
}

interface AuthUser extends User {
  customerId?: string;
  customerData?: CustomerData;
}

export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // Get the user's ID token
        const token = await firebaseUser.getIdToken();
        
        // Fetch customer profile data
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/customers/profile`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.data) {
              setUser({
                ...firebaseUser,
                customerId: data.data.id,
                customerData: data.data
              });
            } else {
              setUser(firebaseUser as AuthUser);
            }
          } else {
            setUser(firebaseUser as AuthUser);
          }
        } catch (error) {
          console.error('Error fetching customer profile:', error);
          setUser(firebaseUser as AuthUser);
        }
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  return { user, loading };
}
