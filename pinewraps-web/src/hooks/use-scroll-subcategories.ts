'use client';

import { useState, useEffect, useCallback } from 'react';
import { Category } from '@/services/api';

interface UseScrollSubcategoriesProps {
  categoryName?: string; // For specific category pages like 'Cakes', 'Flowers', 'Sets'
  selectedCategory?: string; // For shop page with dynamic category selection
}

interface UseScrollSubcategoriesReturn {
  subcategories: Category[];
  isLoading: boolean;
  error: string | null;
  hasSubcategories: boolean;
}

// Cache for subcategories to avoid repeated API calls
const subcategoryCache = new Map<string, {
  data: Category[];
  timestamp: number;
  version: string;
}>();

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Simple cache version based on deployment time
const getCacheVersion = () => {
  return process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA || 'local';
};

export function useScrollSubcategories({
  categoryName,
  selectedCategory
}: UseScrollSubcategoriesProps = {}): UseScrollSubcategoriesReturn {
  
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch subcategories
  const fetchSubcategories = useCallback(async (categoryId: string, categoryNameForFallback?: string) => {
    // Check cache first
    const cacheKey = categoryId || categoryNameForFallback || '';
    const cached = subcategoryCache.get(cacheKey);
    const now = Date.now();
    const currentVersion = getCacheVersion();

    // Check if cache is valid (not expired and same version)
    if (cached &&
        now - cached.timestamp < CACHE_DURATION &&
        cached.version === currentVersion) {
      setSubcategories(cached.data);
      return;
    }

    // Clear cache if version mismatch (deployment cache bust)
    if (cached && cached.version !== currentVersion) {
      subcategoryCache.clear();
    }

    setIsLoading(true);
    setError(null);

    try {
      // First try hierarchical endpoint
      const hierarchicalResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/categories/public/hierarchical?platform=WEB`,
        {
          headers: {
            'X-Platform': 'WEB'
          }
        }
      );

      if (hierarchicalResponse.ok) {
        const hierarchicalData = await hierarchicalResponse.json();
        
        if (hierarchicalData.success && hierarchicalData.data) {
          // Find the target category by ID or name
          const targetCategory = hierarchicalData.data.find((cat: any) => 
            cat.id === categoryId || cat.name === categoryNameForFallback
          );

          if (targetCategory && targetCategory.children && targetCategory.children.length > 0) {
            const subcats = targetCategory.children;
            setSubcategories(subcats);
            // Cache the result with version for cache busting
            subcategoryCache.set(cacheKey, {
              data: subcats,
              timestamp: now,
              version: getCacheVersion()
            });
            return;
          }
        }
      }

      // Fallback: Get all categories and filter for subcategories
      const allCategoriesResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/categories/public`,
        {
          headers: {
            'X-Platform': 'WEB'
          }
        }
      );

      if (allCategoriesResponse.ok) {
        const allCategoriesData = await allCategoriesResponse.json();
        
        if (allCategoriesData.success && allCategoriesData.data) {
          // Find parent category by ID or name
          const parentCategory = allCategoriesData.data.find((cat: any) => 
            cat.id === categoryId || cat.name === categoryNameForFallback
          );

          if (parentCategory) {
            // Find all subcategories that have this category as parent
            const subcats = allCategoriesData.data.filter((cat: any) =>
              cat.parentId === parentCategory.id
            );
            setSubcategories(subcats);
            // Cache the result with version for cache busting
            subcategoryCache.set(cacheKey, {
              data: subcats,
              timestamp: now,
              version: getCacheVersion()
            });
          } else {
            setSubcategories([]);
          }
        }
      } else {
        throw new Error('Failed to fetch categories');
      }
    } catch (err) {
      console.error('Error fetching subcategories:', err);
      setError('Failed to load subcategories');
      setSubcategories([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effect to fetch subcategories when category changes
  useEffect(() => {
    if (selectedCategory) {
      // For shop page - use selected category ID
      fetchSubcategories(selectedCategory);
    } else if (categoryName) {
      // For category pages - use category name
      fetchSubcategories('', categoryName);
    } else {
      // No category selected, clear subcategories
      setSubcategories([]);
      setIsLoading(false);
      setError(null);
    }
  }, [selectedCategory, categoryName, fetchSubcategories]);

  return {
    subcategories,
    isLoading,
    error,
    hasSubcategories: subcategories.length > 0
  };
}
