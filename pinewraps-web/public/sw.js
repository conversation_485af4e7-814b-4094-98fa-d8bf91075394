// Service Worker for Pinewraps Web
// Handles cache management and clearing for checkout flow

const CACHE_NAME = 'pinewraps-web-v8'; // Removed old SubcategoryFilters component completely
const CHECKOUT_CACHE_NAME = 'pinewraps-checkout-v1';

// Install event
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  event.waitUntil(self.clients.claim());
});

// Message event - handle cache clearing requests
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CLEAR_CHECKOUT_CACHE') {
    console.log('Received cache clear request for checkout');
    
    event.waitUntil(
      clearCheckoutCache().then(() => {
        // Send response back to client
        event.ports[0]?.postMessage({ success: true });
      }).catch((error) => {
        console.error('Failed to clear checkout cache:', error);
        event.ports[0]?.postMessage({ success: false, error: error.message });
      })
    );
  }
});

// Function to clear checkout-related cache
async function clearCheckoutCache() {
  try {
    const cacheNames = await caches.keys();
    
    // Clear specific checkout cache
    if (cacheNames.includes(CHECKOUT_CACHE_NAME)) {
      await caches.delete(CHECKOUT_CACHE_NAME);
      console.log('Cleared checkout cache');
    }
    
    // Clear checkout-related entries from main cache
    const mainCache = await caches.open(CACHE_NAME);
    const requests = await mainCache.keys();
    
    const checkoutRequests = requests.filter(request => 
      request.url.includes('/checkout') || 
      request.url.includes('/api/orders') ||
      request.url.includes('checkout') ||
      request.url.includes('_next/static/chunks/pages/checkout')
    );
    
    await Promise.all(
      checkoutRequests.map(request => mainCache.delete(request))
    );
    
    console.log(`Cleared ${checkoutRequests.length} checkout-related cache entries`);
    
    return true;
  } catch (error) {
    console.error('Error clearing checkout cache:', error);
    throw error;
  }
}

// Fetch event - basic caching strategy
self.addEventListener('fetch', (event) => {
  // Only handle GET requests
  if (event.request.method !== 'GET') {
    return;
  }
  
  // Skip caching for checkout pages to ensure fresh content
  if (event.request.url.includes('/checkout')) {
    return;
  }
  
  // Basic cache-first strategy for static assets
  if (event.request.url.includes('/_next/static/') || 
      event.request.url.includes('/images/') ||
      event.request.url.includes('/icons/')) {
    
    event.respondWith(
      caches.match(event.request).then((response) => {
        if (response) {
          return response;
        }
        
        return fetch(event.request).then((response) => {
          // Don't cache if not a valid response
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }
          
          const responseToCache = response.clone();
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseToCache);
          });
          
          return response;
        });
      })
    );
  }
});

// Handle cache cleanup on version updates
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          // Delete old cache versions
          if (cacheName !== CACHE_NAME && cacheName !== CHECKOUT_CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      ).then(() => {
        // Force clear all caches to ensure old components are removed
        console.log('Clearing all caches to remove old filter components');
        return caches.keys().then((cacheNames) => {
          return Promise.all(
            cacheNames.map((cacheName) => caches.delete(cacheName))
          );
        });
      });
    })
  );
});
