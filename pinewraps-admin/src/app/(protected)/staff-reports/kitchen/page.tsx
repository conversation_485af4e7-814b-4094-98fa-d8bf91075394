'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  ChefHat,
  Clock, 
  TrendingUp, 
  Award, 
  Target,
  Download,
  RefreshCw,
  Users,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';

import staffReportsService, { StaffPerformanceMetrics } from '@/services/staff-reports.service';

const timeRanges = [
  { value: '7d', label: 'Last 7 days' },
  { value: '14d', label: 'Last 14 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '3m', label: 'Last 3 months' },
  { value: 'all', label: 'All time' },
];

export default function KitchenStaffPage() {
  const router = useRouter();
  const [kitchenStaff, setKitchenStaff] = useState<StaffPerformanceMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('30d');

  // Optimized kitchen staff data fetching
  const fetchKitchenStaff = async () => {
    try {
      setIsRefreshing(true);
      console.log('Fetching kitchen staff data...');

      const params: any = {
        timeRange,
        department: 'kitchen'
      };

      const startTime = performance.now();
      const data = await staffReportsService.getStaffPerformance(params);
      const endTime = performance.now();

      console.log(`Kitchen staff data loaded in ${Math.round(endTime - startTime)}ms`);
      setKitchenStaff(data);
    } catch (error) {
      console.error('Error fetching kitchen staff:', error);
      toast.error('Failed to load kitchen staff data');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchKitchenStaff();
  }, [timeRange]);

  // Calculate department summary
  const departmentSummary = {
    totalStaff: kitchenStaff.length,
    totalOrders: kitchenStaff.reduce((sum, staff) => sum + staff.ordersCompleted, 0),
    avgProductivity: kitchenStaff.length > 0 
      ? kitchenStaff.reduce((sum, staff) => sum + staff.productivityScore, 0) / kitchenStaff.length 
      : 0,
    avgQuality: kitchenStaff.length > 0 
      ? kitchenStaff.reduce((sum, staff) => sum + staff.qualityScore, 0) / kitchenStaff.length 
      : 0,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-red-500 text-white">
            <ChefHat className="h-6 w-6" />
          </div>
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Kitchen Staff</h2>
            <p className="text-muted-foreground">
              Food preparation and kitchen operations performance
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {timeRanges.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={fetchKitchenStaff}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Department Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Kitchen Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departmentSummary.totalStaff}</div>
            <p className="text-xs text-muted-foreground">
              Active kitchen team members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders Processed</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departmentSummary.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              Total kitchen orders completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Productivity</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departmentSummary.avgProductivity.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Kitchen team productivity
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quality Score</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departmentSummary.avgQuality.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Average quality rating
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Kitchen Staff Table */}
      <Card>
        <CardHeader>
          <CardTitle>Kitchen Staff Performance</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                </div>
              ))}
            </div>
          ) : kitchenStaff.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full min-w-[800px] table-auto">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3">Staff Name</th>
                    <th className="text-left p-3">Orders Completed</th>
                    <th className="text-left p-3">Avg Processing Time</th>
                    <th className="text-left p-3">Quality Score</th>
                    <th className="text-left p-3">Productivity</th>
                    <th className="text-left p-3">Orders/Hour</th>
                    <th className="text-left p-3">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {kitchenStaff.map((staff) => {
                    const qualityRating = staffReportsService.getPerformanceRating(staff.qualityScore);
                    const productivityRating = staffReportsService.getPerformanceRating(staff.productivityScore);
                    
                    return (
                      <tr key={staff.staffId} className="border-b hover:bg-muted/50">
                        <td className="p-3">
                          <div className="flex items-center space-x-3">
                            <div className="p-2 rounded-full bg-red-100 text-red-600">
                              <ChefHat className="h-4 w-4" />
                            </div>
                            <div className="min-w-0">
                              <p className="font-medium truncate max-w-[150px]" title={staff.staffName}>{staff.staffName}</p>
                              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <span>Kitchen Staff</span>
                                {staff.isMultiRole && (
                                  <Badge variant="secondary" className="text-xs">
                                    +{staff.departments.length - 1} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="p-3 font-medium">{staff.ordersCompleted}</td>
                        <td className="p-3">{staffReportsService.formatProcessingTime(staff.averageProcessingTime)}</td>
                        <td className="p-3">
                          <div className="flex items-center space-x-2">
                            <span className={qualityRating.color}>
                              {staff.qualityScore.toFixed(1)}%
                            </span>
                            {staff.ordersSentBack > 0 && (
                              <Badge variant="destructive" className="text-xs">
                                {staff.ordersSentBack} sent back
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="p-3">
                          <span className={productivityRating.color}>
                            {staff.productivityScore.toFixed(1)}%
                          </span>
                        </td>
                        <td className="p-3">{staff.ordersPerHour.toFixed(1)}</td>
                        <td className="p-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/staff-reports/${staff.staffId}`)}
                          >
                            View Details
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No Kitchen Staff Found</h3>
              <p>No kitchen staff members found for the selected time period.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
