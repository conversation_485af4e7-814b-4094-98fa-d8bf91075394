'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Palette,
  Clock, 
  TrendingUp, 
  Award, 
  Target,
  Download,
  RefreshCw,
  Users,
  AlertTriangle,
  Sparkles
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';

import staffReportsService, { StaffPerformanceMetrics } from '@/services/staff-reports.service';

const timeRanges = [
  { value: '7d', label: 'Last 7 days' },
  { value: '14d', label: 'Last 14 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '3m', label: 'Last 3 months' },
  { value: 'all', label: 'All time' },
];

export default function DesignStaffPage() {
  const router = useRouter();
  const [designStaff, setDesignStaff] = useState<StaffPerformanceMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('30d');

  // Fetch design staff data
  const fetchDesignStaff = async () => {
    try {
      setIsRefreshing(true);
      
      const params: any = { 
        timeRange,
        department: 'design'
      };
      
      const data = await staffReportsService.getStaffPerformance(params);
      setDesignStaff(data);
    } catch (error) {
      console.error('Error fetching design staff:', error);
      toast.error('Failed to load design staff data');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchDesignStaff();
  }, [timeRange]);



  // Calculate department summary
  const departmentSummary = {
    totalStaff: designStaff.length,
    totalOrders: designStaff.reduce((sum, staff) => sum + staff.ordersCompleted, 0),
    avgProductivity: designStaff.length > 0 
      ? designStaff.reduce((sum, staff) => sum + staff.productivityScore, 0) / designStaff.length 
      : 0,
    avgQuality: designStaff.length > 0 
      ? designStaff.reduce((sum, staff) => sum + staff.qualityScore, 0) / designStaff.length 
      : 0,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-blue-500 text-white">
            <Palette className="h-6 w-6" />
          </div>
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Design Staff</h2>
            <p className="text-muted-foreground">
              Custom design and creative work performance
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {timeRanges.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={fetchDesignStaff}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          

        </div>
      </div>

      {/* Department Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Design Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departmentSummary.totalStaff}</div>
            <p className="text-xs text-muted-foreground">
              Active design team members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Designs Completed</CardTitle>
            <Sparkles className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departmentSummary.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              Total custom designs created
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Productivity</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departmentSummary.avgProductivity.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Design team productivity
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quality Score</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departmentSummary.avgQuality.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Average design quality
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Design Staff Table */}
      <Card>
        <CardHeader>
          <CardTitle>Design Staff Performance</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                </div>
              ))}
            </div>
          ) : designStaff.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full min-w-[800px] table-auto">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3">Staff Name</th>
                    <th className="text-left p-3">Designs Completed</th>
                    <th className="text-left p-3">Avg Design Time</th>
                    <th className="text-left p-3">Quality Score</th>
                    <th className="text-left p-3">Productivity</th>
                    <th className="text-left p-3">Designs/Hour</th>
                    <th className="text-left p-3">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {designStaff.map((staff) => {
                    const qualityRating = staffReportsService.getPerformanceRating(staff.qualityScore);
                    const productivityRating = staffReportsService.getPerformanceRating(staff.productivityScore);
                    
                    return (
                      <tr key={staff.staffId} className="border-b hover:bg-muted/50">
                        <td className="p-3">
                          <div className="flex items-center space-x-3">
                            <div className="p-2 rounded-full bg-blue-100 text-blue-600">
                              <Palette className="h-4 w-4" />
                            </div>
                            <div>
                              <p className="font-medium">{staff.staffName}</p>
                              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <span>Design Staff</span>
                                {staff.isMultiRole && (
                                  <Badge variant="secondary" className="text-xs">
                                    +{staff.departments.length - 1} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="p-3 font-medium">{staff.ordersCompleted}</td>
                        <td className="p-3">{staffReportsService.formatProcessingTime(staff.averageProcessingTime)}</td>
                        <td className="p-3">
                          <div className="flex items-center space-x-2">
                            <span className={qualityRating.color}>
                              {staff.qualityScore.toFixed(1)}%
                            </span>
                            {staff.ordersSentBack > 0 && (
                              <Badge variant="destructive" className="text-xs">
                                {staff.ordersSentBack} revisions
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="p-3">
                          <span className={productivityRating.color}>
                            {staff.productivityScore.toFixed(1)}%
                          </span>
                        </td>
                        <td className="p-3">{staff.ordersPerHour.toFixed(1)}</td>
                        <td className="p-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/staff-reports/${staff.staffId}`)}
                          >
                            View Details
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No Design Staff Found</h3>
              <p>No design staff members found for the selected time period.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
