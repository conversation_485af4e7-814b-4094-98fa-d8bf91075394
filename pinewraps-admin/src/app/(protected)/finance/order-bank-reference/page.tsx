'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { formatDate } from '@/lib/utils';
import { bankReferenceService, type BankReferenceOrder } from '@/services/bank-reference.service';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, CalendarIcon, ChevronLeft, ChevronRight, Search } from 'lucide-react';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

export default function OrderBankReferencePage() {
  const [activeTab, setActiveTab] = useState<'pos' | 'online'>('pos');
  const [selectedOrder, setSelectedOrder] = useState<BankReferenceOrder | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [bankReference, setBankReference] = useState('');
  const [bankReferenceDate, setBankReferenceDate] = useState<Date | null>(null);
  const [bankReferences, setBankReferences] = useState<Array<{
    paymentId: string;
    paymentMethod: string;
    amount: number;
    reference: string;
    date: Date | null;
  }>>([]);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [orders, setOrders] = useState<BankReferenceOrder[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  // Helper function to get effective order total after partial refunds
  const getEffectiveOrderTotal = (order: BankReferenceOrder) => {
    const originalTotal = order.originalTotal || order.total || 0;
    const partialRefundAmount = order.partialRefundAmount || 0;
    return Math.max(0, originalTotal - partialRefundAmount);
  };

  // Helper function to check if order is partially refunded
  const isPartiallyRefunded = (order: BankReferenceOrder) => {
    // Use computed status from backend if available, otherwise fallback to manual check
    if (order.isPartiallyRefunded !== undefined) {
      return order.isPartiallyRefunded;
    }
    
    const orderStatus = order.status?.toString().toUpperCase();
    const paymentStatus = order.paymentStatus?.toString().toUpperCase();
    const hasPartialRefundAmount = order.partialRefundAmount && order.partialRefundAmount > 0;
    
    return (orderStatus === 'PARTIALLY_REFUNDED' || 
            paymentStatus === 'PARTIALLY_REFUNDED' || 
            orderStatus === 'PARTIAL_REFUND') && hasPartialRefundAmount;
  };

  // Helper function to check if order is fully refunded
  const isFullyRefunded = (order: BankReferenceOrder) => {
    // Use computed status from backend if available, otherwise fallback to manual check
    if (order.isFullyRefunded !== undefined) {
      return order.isFullyRefunded;
    }
    
    const orderStatus = order.status?.toString().toUpperCase();
    const paymentStatus = order.paymentStatus?.toString().toUpperCase();
    return orderStatus === 'REFUNDED' || paymentStatus === 'REFUNDED';
  };

  // Helper function to check if order is cancelled
  const isCancelled = (order: BankReferenceOrder) => {
    // Use computed status from backend if available, otherwise fallback to manual check
    if (order.isCancelled !== undefined) {
      return order.isCancelled;
    }
    
    const orderStatus = order.status?.toString().toUpperCase();
    const paymentStatus = order.paymentStatus?.toString().toUpperCase();
    return orderStatus === 'CANCELLED' || paymentStatus === 'CANCELLED';
  };

  const fetchOrders = async (page: number = pagination.page, search: string = searchTerm) => {
    try {
      setLoading(true);
      const response = await bankReferenceService.getOrders({
        orderType: activeTab,
        page,
        limit: pagination.limit,
        search: search.trim() || undefined,
      });
      setOrders(response.data.orders);
      setPagination(prev => ({
        ...prev,
        page,
        total: response.data.pagination.total
      }));
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch orders. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setPagination(prev => ({ ...prev, page: 1 }));
    setSearchTerm('');
    fetchOrders(1, '');
  }, [activeTab]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchOrders(1, value);
  };

  const handleEditClick = (order: BankReferenceOrder) => {
    console.log('Order payment breakdown:', order.paymentBreakdown);
    console.log('Order bank references:', order.bankReferences);
    console.log('Order bank reference dates:', order.bankReferenceDates);

    setSelectedOrder(order);
    setBankReference(order.bankReference || '');
    setBankReferenceDate(order.bankReferenceDate ? new Date(order.bankReferenceDate) : null);

    // Initialize bank references for multiple payments
    if (order.paymentBreakdown && order.paymentBreakdown.length > 1) {
      const initialReferences = order.paymentBreakdown.map(payment => {
        // Find existing reference for this payment
        const existingRef = order.bankReferences?.find(ref => ref.paymentId === payment.id);

        return {
          paymentId: payment.id,
          paymentMethod: payment.method,
          amount: payment.amount,
          reference: existingRef?.reference || '',
          date: existingRef?.date ? new Date(existingRef.date) : null,
        };
      });
      setBankReferences(initialReferences);
    } else {
      setBankReferences([]);
    }

    setIsModalOpen(true);
  };

  const handlePageChange = (newPage: number) => {
    fetchOrders(newPage);
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);
  const startItem = (pagination.page - 1) * pagination.limit + 1;
  const endItem = Math.min(pagination.page * pagination.limit, pagination.total);

  const handleSave = async () => {
    if (!selectedOrder) {
      return;
    }

    // Check if we have multiple payments
    const hasMultiplePayments = selectedOrder.paymentBreakdown && selectedOrder.paymentBreakdown.length > 1;

    if (hasMultiplePayments) {
      // Validate multiple bank references - only for payment methods that need them
      const invalidReferences = bankReferences.filter(ref => {
        const needsBankRef = !['PARTIAL', 'SPLIT', 'PAY_LATER'].includes(ref.paymentMethod);
        return needsBankRef && (!ref.reference.trim() || !ref.date);
      });

      if (invalidReferences.length > 0) {
        toast({
          title: 'Error',
          description: 'Payment methods requiring bank references must have both reference and date',
          variant: 'destructive',
        });
        return;
      }
    } else {
      // Validate single bank reference
      if (!bankReference.trim()) {
        toast({
          title: 'Error',
          description: 'Bank reference is required',
          variant: 'destructive',
        });
        return;
      }

      if (!bankReferenceDate) {
        toast({
          title: 'Error',
          description: 'Bank reference date is required',
          variant: 'destructive',
        });
        return;
      }
    }

    try {
      setUpdating(true);

      if (hasMultiplePayments) {
        // Send multiple bank references - only for payment methods that need them
        const formattedReferences = bankReferences
          .filter(ref => {
            const needsBankRef = !['PARTIAL', 'SPLIT', 'PAY_LATER'].includes(ref.paymentMethod);
            return needsBankRef && ref.reference.trim() && ref.date;
          })
          .map(ref => ({
            paymentId: ref.paymentId,
            paymentMethod: ref.paymentMethod,
            amount: ref.amount,
            reference: ref.reference.trim(),
            date: ref.date!.toISOString(),
            addedBy: '', // Will be set by the backend
          }));

        // Use the first valid date for the main reference date
        const firstValidDate = formattedReferences.length > 0
          ? formattedReferences[0].date
          : new Date().toISOString();

        await bankReferenceService.updateBankReference(selectedOrder.id, {
          bankReferenceDate: firstValidDate,
          orderType: selectedOrder.orderType,
          bankReferences: formattedReferences,
        });
      } else {
        // Send single bank reference (legacy mode)
        await bankReferenceService.updateBankReference(selectedOrder.id, {
          bankReference: bankReference.trim(),
          bankReferenceDate: bankReferenceDate!.toISOString(),
          orderType: selectedOrder.orderType,
        });
      }

      toast({
        title: 'Success',
        description: 'Bank reference updated successfully',
      });

      // Refresh orders on current page
      await fetchOrders(pagination.page);
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error updating bank reference:', error);
      toast({
        title: 'Error',
        description: 'Failed to update bank reference. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setUpdating(false);
    }
  };

  const OrderTable = ({ orders }: { orders: BankReferenceOrder[] }) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Order ID</TableHead>
          <TableHead>Date & Time</TableHead>
          <TableHead>Customer</TableHead>
          <TableHead>Phone</TableHead>
          <TableHead className="text-right">Amount</TableHead>
          <TableHead>Payment Method</TableHead>
          <TableHead>Bank Reference</TableHead>
          <TableHead>Bank Reference Date</TableHead>
          <TableHead>Updated By</TableHead>
          <TableHead>Action</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {orders.map((order) => (
          <TableRow key={order.id}>
            <TableCell>{order.orderNumber}</TableCell>
            <TableCell>{formatDate(order.createdAt)}</TableCell>
            <TableCell>{order.customerName}</TableCell>
            <TableCell>{order.customerPhone || '-'}</TableCell>
            <TableCell className="text-right">
              {isPartiallyRefunded(order) ? (
                <div className="flex flex-col items-end">
                  <div className="font-medium text-green-600">
                    AED {getEffectiveOrderTotal(order).toFixed(2)}
                  </div>
                  <div className="text-xs text-gray-500">
                    Original: AED {(order.originalTotal || order.total).toFixed(2)}
                  </div>
                  <div className="text-xs text-red-600">
                    Refunded: AED {order.partialRefundAmount!.toFixed(2)}
                  </div>
                </div>
              ) : (
                <div className="font-medium">
                  AED {order.total.toFixed(2)}
                </div>
              )}
            </TableCell>
            <TableCell>
              {order.paymentBreakdown && order.paymentBreakdown.length > 1 ? (
                <div className="flex flex-col gap-1">
                  {order.paymentBreakdown.map((payment, index) => (
                    <div key={index} className="text-xs">
                      {payment.method}: AED {payment.amount.toFixed(2)}
                    </div>
                  ))}
                </div>
              ) : (
                order.paymentMethod
              )}
            </TableCell>
            <TableCell>
              {order.bankReferences && order.bankReferences.length > 0 ? (
                <div className="flex flex-col gap-1">
                  {order.bankReferences.map((ref, index) => (
                    <div key={index} className="text-xs">
                      <div className="font-medium">{ref.reference}</div>
                      <div className="text-gray-500">
                        {ref.paymentMethod}: AED {ref.amount?.toFixed(2)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                order.bankReference || '-'
              )}
            </TableCell>
            <TableCell>
              {order.bankReferences && order.bankReferences.length > 0 ? (
                <div className="flex flex-col gap-1">
                  {order.bankReferences.map((ref, index) => (
                    <div key={index} className="text-xs">
                      {ref.date ? formatDate(ref.date) : '-'}
                    </div>
                  ))}
                </div>
              ) : (
                order.bankReferenceDate ? formatDate(order.bankReferenceDate) : '-'
              )}
            </TableCell>
            <TableCell>{order.bankReferenceBy || '-'}</TableCell>
            <TableCell>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleEditClick(order)}
              >
                Edit
              </Button>
            </TableCell>
          </TableRow>
        ))}
        {orders.length === 0 && !loading && (
          <TableRow>
            <TableCell colSpan={10} className="text-center py-8">
              No orders found
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Heading title="Order Bank Reference" description="Manage bank references for orders" />
        </div>
      </div>
      <Separator />
      <Card>
        <CardContent className="pt-6">
          <div className="mb-4">
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by order number, customer name, or phone..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'pos' | 'online')}>
            <TabsList className="grid w-[400px] grid-cols-2">
              <TabsTrigger value="pos">POS Orders</TabsTrigger>
              <TabsTrigger value="online">Online Orders</TabsTrigger>
            </TabsList>
            <TabsContent value="pos" className="mt-6">
              {loading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <>
                  <OrderTable orders={orders.filter((o) => o.orderType === 'POS')} />
                  {pagination.total > 0 && (
                    <div className="flex items-center justify-between px-2 py-4">
                      <div className="text-sm text-muted-foreground">
                        Showing {startItem} to {endItem} of {pagination.total} orders
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={pagination.page === 1}
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>
                        <div className="flex items-center space-x-1">
                          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            const pageNum = Math.max(1, Math.min(totalPages - 4, pagination.page - 2)) + i;
                            return (
                              <Button
                                key={pageNum}
                                variant={pageNum === pagination.page ? "default" : "outline"}
                                size="sm"
                                onClick={() => handlePageChange(pageNum)}
                                className="w-8 h-8 p-0"
                              >
                                {pageNum}
                              </Button>
                            );
                          })}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={pagination.page === totalPages}
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </TabsContent>
            <TabsContent value="online" className="mt-6">
              {loading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <>
                  <OrderTable orders={orders.filter((o) => o.orderType === 'ONLINE')} />
                  {pagination.total > 0 && (
                    <div className="flex items-center justify-between px-2 py-4">
                      <div className="text-sm text-muted-foreground">
                        Showing {startItem} to {endItem} of {pagination.total} orders
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={pagination.page === 1}
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>
                        <div className="flex items-center space-x-1">
                          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            const pageNum = Math.max(1, Math.min(totalPages - 4, pagination.page - 2)) + i;
                            return (
                              <Button
                                key={pageNum}
                                variant={pageNum === pagination.page ? "default" : "outline"}
                                size="sm"
                                onClick={() => handlePageChange(pageNum)}
                                className="w-8 h-8 p-0"
                              >
                                {pageNum}
                              </Button>
                            );
                          })}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={pagination.page === totalPages}
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Update Bank Reference</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label>Order Number</Label>
              <div className="col-span-3">{selectedOrder?.orderNumber}</div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label>Status</Label>
              <div className="col-span-3">
                {selectedOrder && isPartiallyRefunded(selectedOrder) ? (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    Partially Refunded
                  </span>
                ) : selectedOrder && isFullyRefunded(selectedOrder) ? (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Refunded
                  </span>
                ) : selectedOrder && isCancelled(selectedOrder) ? (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    Cancelled
                  </span>
                ) : (
                  <div className="flex flex-col">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Active
                    </span>
                    <div className="text-xs text-gray-500 mt-1 space-y-1">
                      {selectedOrder?.status && (
                        <div>Order Status: {selectedOrder.status}</div>
                      )}
                      {selectedOrder?.paymentStatus && (
                        <div>Payment Status: {selectedOrder.paymentStatus}</div>
                      )}
                      {selectedOrder?.partialRefundAmount && (
                        <div>Partial Refund: AED {selectedOrder.partialRefundAmount.toFixed(2)}</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label>Amount</Label>
              <div className="col-span-3">
                {selectedOrder && isPartiallyRefunded(selectedOrder) ? (
                  <div className="space-y-1">
                    <div className="font-medium text-green-600">
                      Current Value: AED {getEffectiveOrderTotal(selectedOrder).toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-600">
                      Original Total: AED {(selectedOrder.originalTotal || selectedOrder.total).toFixed(2)}
                    </div>
                    <div className="text-sm text-red-600">
                      Refunded Amount: AED {selectedOrder.partialRefundAmount!.toFixed(2)}
                    </div>
                  </div>
                ) : (
                  <div className="font-medium">
                    AED {selectedOrder?.total.toFixed(2)}
                  </div>
                )}
              </div>
            </div>
            {selectedOrder?.orderType === 'POS' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label>Payment Reference</Label>
                <div className="col-span-3">
                  {selectedOrder?.paymentReference ? (
                    <span>{selectedOrder.paymentReference}</span>
                  ) : selectedOrder?.paymentReferences?.length ? (
                    <div className="flex flex-col gap-1">
                      {selectedOrder.paymentReferences.map((ref, index) => (
                        <span key={index} className="text-sm">{ref}</span>
                      ))}
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-sm">No payment reference found</span>
                  )}
                </div>
              </div>
            )}

            {/* Show multiple payment methods if applicable */}
            {selectedOrder?.paymentBreakdown && selectedOrder.paymentBreakdown.length > 1 ? (
              <div className="col-span-4">
                <Label className="text-base font-medium mb-4 block">Payment Breakdown & Bank References</Label>
                <div className="space-y-4 border rounded-lg p-4">
                  {bankReferences.map((ref, index) => {
                    // Check if this payment method needs bank reference
                    const needsBankRef = !['PARTIAL', 'SPLIT', 'PAY_LATER'].includes(ref.paymentMethod);

                    return (
                      <div key={ref.paymentId} className="grid grid-cols-1 gap-3 p-3 border rounded bg-gray-50">
                        <div className="flex justify-between items-center">
                          <div className="flex flex-col">
                            <span className="font-medium">{ref.paymentMethod}</span>
                            {selectedOrder?.paymentBreakdown?.[index]?.reference && (
                              <span className="text-xs text-gray-500">
                                Pay Ref: {selectedOrder.paymentBreakdown[index].reference}
                              </span>
                            )}
                          </div>
                          <span className="text-sm text-gray-600">AED {ref.amount.toFixed(2)}</span>
                        </div>

                        {needsBankRef ? (
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <Label htmlFor={`bankRef-${index}`} className="text-sm">Bank Reference</Label>
                              <Input
                                id={`bankRef-${index}`}
                                value={ref.reference}
                                onChange={(e) => {
                                  const newRefs = [...bankReferences];
                                  newRefs[index].reference = e.target.value;
                                  setBankReferences(newRefs);
                                }}
                                placeholder="Enter bank reference"
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor={`bankDate-${index}`} className="text-sm">Reference Date</Label>
                              <div className="relative mt-1">
                                <DatePicker
                                  selected={ref.date}
                                  onChange={(date: Date) => {
                                    const newRefs = [...bankReferences];
                                    newRefs[index].date = date;
                                    setBankReferences(newRefs);
                                  }}
                                  dateFormat="MMM d, yyyy"
                                  className="w-full h-9 rounded-md border border-input bg-background px-3 py-2 text-sm"
                                  showPopperArrow={false}
                                />
                                <CalendarIcon className="absolute right-3 top-2 h-4 w-4 opacity-50" />
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500 italic">
                            No bank reference required for {ref.paymentMethod} payments
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              // Single payment method (legacy mode)
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="bankReference">Bank Reference</Label>
                  <Input
                    id="bankReference"
                    value={bankReference}
                    onChange={(e) => setBankReference(e.target.value)}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="bankReferenceDate">Reference Date</Label>
                  <div className="col-span-3 relative">
                    <DatePicker
                      selected={bankReferenceDate}
                      onChange={(date: Date) => setBankReferenceDate(date)}
                      dateFormat="MMMM d, yyyy"
                      className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      showPopperArrow={false}
                    />
                    <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 opacity-50" />
                  </div>
                </div>
              </>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={updating}>
              {updating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
