'use client';

import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal, Pencil, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useRouter, useSearchParams } from 'next/navigation';
import { formatDate } from '@/lib/utils';
import { useAuth } from '@/providers/auth-provider';

export type PurchaseOrder = {
  id: string;
  orderNumber: string;
  supplier: {
    id: string;
    name: string;
    code: string;
  };
  paymentTerms: string;
  status: string;
  total: number;
  tax: number;
  subtotal: number;
  additionalCharge: number;
  invoiceNumber?: string;
  invoiceDate?: string;
  createdAt: string;
  updatedAt: string;
  items?: {
    id: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }[];
};

const ActionsCell = ({ row }: { row: any }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const order = row.original;
  const { user } = useAuth();
  console.log('User in ActionsCell:', user);
  // Show edit option for everyone for now
  const isSuperAdmin = true; // user?.role === 'SUPER_ADMIN';

  // Create return URL with current search params
  const createReturnUrl = () => {
    const currentParams = searchParams.toString();
    return currentParams ? `?${currentParams}` : '';
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-muted">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        <DropdownMenuLabel className="text-xs">Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => {
            const returnUrl = createReturnUrl();
            router.push(`/inventory/purchase-orders/${order.id}${returnUrl ? `?returnUrl=${encodeURIComponent('/inventory/purchase-orders' + returnUrl)}` : ''}`);
          }}
          className="text-xs"
        >
          <Eye className="mr-2 h-3 w-3" />
          View Details
        </DropdownMenuItem>
        {isSuperAdmin && (
          <DropdownMenuItem
            onClick={() => {
              const returnUrl = createReturnUrl();
              router.push(`/inventory/purchase-orders/${order.id}/edit${returnUrl ? `?returnUrl=${encodeURIComponent('/inventory/purchase-orders' + returnUrl)}` : ''}`);
            }}
            className="text-xs"
          >
            <Pencil className="mr-2 h-3 w-3" />
            Edit Order
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const getStatusColor = (status: string) => {
  const statusColors: Record<string, 'default' | 'secondary' | 'outline' | 'destructive'> = {
    DRAFT: 'secondary',
    PENDING: 'outline',
    APPROVED: 'secondary',
    CANCELLED: 'destructive',
    COMPLETED: 'default',
  };
  return statusColors[status] || 'default';
};

export const columns: ColumnDef<PurchaseOrder>[] = [
  {
    accessorKey: 'orderNumber',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-8 px-2"
      >
        PO Number
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <Button
        variant="link"
        onClick={() => {
          const router = useRouter();
          router.push(`/inventory/purchase-orders/${row.original.id}`);
        }}
        className="h-auto p-0 text-left justify-start max-w-[140px] truncate"
      >
        {row.getValue('orderNumber')}
      </Button>
    ),
    size: 140,
  },
  {
    accessorKey: 'supplier.name',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-8 px-2"
      >
        Supplier
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <div className="font-medium text-sm leading-tight max-w-[200px] truncate">
        {row.original.supplier.name}
      </div>
    ),
    size: 200,
  },
  {
    accessorKey: 'invoiceNumber',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-8 px-2"
      >
        Invoice Number
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const invoiceNumber = row.original.invoiceNumber;
      return (
        <div className="max-w-[160px]">
          {invoiceNumber ? (
            <span className="text-sm font-medium break-words">{invoiceNumber}</span>
          ) : (
            <span className="text-muted-foreground text-sm">-</span>
          )}
        </div>
      );
    },
    size: 160,
  },
  {
    accessorKey: 'invoiceDate',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-8 px-2"
      >
        Invoice Date
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const invoiceDate = row.original.invoiceDate;
      return (
        <div className="max-w-[140px]">
          {invoiceDate ? (
            <span className="text-sm whitespace-nowrap">{new Date(invoiceDate).toLocaleDateString('en-GB')}</span>
          ) : (
            <span className="text-muted-foreground text-sm">-</span>
          )}
        </div>
      );
    },
    size: 140,
  },

  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-8 px-2"
      >
        Created Date
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const createdAt = row.original.createdAt;
      return createdAt ? (
        <span className="text-sm">{new Date(createdAt).toLocaleDateString('en-GB')}</span>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
    size: 130,
  },
  {
    accessorKey: 'total',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-8 px-2"
      >
        Total
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const order = row.original;
      
      // Calculate the correct total from items if available
      let calculatedTotal = parseFloat(row.getValue('total'));
      
      if (order.items && order.items.length > 0) {
        // Calculate subtotal from items using stored totalPrice
        const itemsSubtotal = order.items.reduce((sum: number, item: any) => {
          return sum + parseFloat(item.total || 0);
        }, 0);
        
        // Calculate total: items subtotal + tax + additional charges
        const tax = parseFloat(String(order.tax || 0));
        const additionalCharge = parseFloat(String(order.additionalCharge || 0));
        calculatedTotal = itemsSubtotal + tax + additionalCharge;
      }
      
      const formatted = new Intl.NumberFormat('en-AE', {
        style: 'currency',
        currency: 'AED',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(calculatedTotal);
      
      return <span className="font-medium text-sm">{formatted}</span>;
    },
    size: 140,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-8 px-2"
      >
        Status
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <Badge variant={getStatusColor(row.original.status)} className="text-xs">
        {row.original.status.replace('_', ' ')}
      </Badge>
    ),
    size: 120,
  },
  {
    id: 'actions',
    header: '',
    cell: ActionsCell,
    size: 50,
  },
];
