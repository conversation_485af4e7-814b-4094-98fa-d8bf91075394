'use client';

import { useEffect, useState, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Loader2, <PERSON>cil, FileText, FileDown, Trash2, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { toast } from 'sonner';

import { PurchaseOrderForm } from '@/components/purchase-orders/PurchaseOrderForm';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Heading } from '@/components/ui/heading';
import api from '@/lib/api';
import { getFirebaseToken } from '@/lib/firebase';

// Helper function to format currency
const formatCurrency = (amount: number | string | null | undefined) => {
  if (amount === null || amount === undefined) return 'AED 0.00';
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  return `AED ${num.toFixed(2)}`;
};

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function PurchaseOrderPage({ params }: PageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isSuperAdmin, user } = useAuth();

  // Get return URL from search params
  const returnUrl = searchParams.get('returnUrl') || '/inventory/purchase-orders';
  
  // Debug logs
  console.log('Auth state:', {
    isSuperAdmin,
    userRole: user?.role,
    user: user
  });
  const [purchaseOrder, setPurchaseOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const { id } = use(params);

  useEffect(() => {
    const fetchPurchaseOrder = async () => {
      if (!id) {
        setError('Invalid purchase order ID');
        setLoading(false);
        return;
      }

      try {
        const token = await getFirebaseToken();
        const response = await api.get(`/api/purchase-orders/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        setPurchaseOrder(response.data.data);
      } catch (error: any) {
        console.error('Error fetching purchase order:', error);
        setError(error.response?.data?.message || 'Failed to load purchase order');
        if (error.response?.status === 404) {
          router.push('/404');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchPurchaseOrder();
  }, [id, router]);

  if (loading) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  if (!purchaseOrder) {
    return null;
  }

  // Calculate correct totals from items
  const calculateTotalsFromItems = () => {
    if (!purchaseOrder.items || purchaseOrder.items.length === 0) {
      return {
        calculatedSubtotal: parseFloat(purchaseOrder.subtotal || 0),
        calculatedTotal: parseFloat(purchaseOrder.total || 0)
      };
    }

    // Calculate subtotal from items using stored totalPrice values
    const calculatedSubtotal = purchaseOrder.items.reduce((sum: number, item: any) => {
      // Use stored total price from each item
      const totalPrice = Number(item.total) || 0;
      
      // Debug logging for troubleshooting
      console.log(`Item: ${item.item?.name || 'Unknown'}, Stored Total: ${totalPrice}`);
      
      // Special logging for zero-priced items (free items)
      if (totalPrice === 0) {
        console.warn(`🆓 FREE ITEM DETECTED: ${item.item?.name || 'Unknown'} - Stored Total: ${totalPrice}`);
      }
      
      return sum + totalPrice;
    }, 0);
    
    // Debug the final calculated subtotal
    console.log(`Final calculated subtotal: ${calculatedSubtotal}`);

    // Calculate total: items subtotal + tax + additional charges
    const tax = parseFloat(purchaseOrder.tax || 0);
    const additionalCharge = parseFloat(purchaseOrder.additionalCharge || 0);
    const calculatedTotal = calculatedSubtotal + tax + additionalCharge;

    return { calculatedSubtotal, calculatedTotal };
  };

  const { calculatedSubtotal, calculatedTotal } = calculateTotalsFromItems();

  return (
    <div className="flex-1 space-y-6 p-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(returnUrl)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Purchase Order #{purchaseOrder.orderNumber}</h1>
            <p className="text-muted-foreground mt-1">Created on {new Date(purchaseOrder.createdAt).toLocaleDateString()}</p>
          </div>
        </div>
        <div className="flex gap-3">
          {/* Download Invoice PDF Button */}
          {purchaseOrder.invoicePdfUrl && (
            <Button 
              variant="outline"
              onClick={() => {
                // Direct download from Firebase Storage URL
                const link = document.createElement('a');
                link.href = purchaseOrder.invoicePdfUrl;
                link.setAttribute('download', `Invoice-${purchaseOrder.orderNumber}.pdf`);
                link.setAttribute('target', '_blank');
                document.body.appendChild(link);
                link.click();
                link.remove();
              }}
            >
              <FileDown className="h-4 w-4 mr-2" />
              Download Invoice
            </Button>
          )}

          {/* Download PO PDF Button */}
          <Button 
            variant="outline"
            onClick={async () => {
              try {
                const token = await getFirebaseToken();
                // Create a download link instead of opening in a new tab
                const response = await api.get(`/api/purchase-orders/${id}/pdf?token=${token}`, {
                  responseType: 'blob',
                });
                
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `PO-${purchaseOrder.orderNumber}.pdf`);
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);
              } catch (error) {
                console.error('Error getting token for PDF download:', error);
                toast.error('Failed to download PDF. Please try again.');
              }
            }}
          >
            <FileText className="h-4 w-4 mr-2" />
            Download PO
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              const editUrl = `/inventory/purchase-orders/${id}/edit${returnUrl !== '/inventory/purchase-orders' ? `?returnUrl=${encodeURIComponent(returnUrl)}` : ''}`;
              router.push(editUrl);
            }}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit Order
          </Button>

          {/* Delete Button - Only visible to super admins */}
          {isSuperAdmin && (
            <Button 
              variant="destructive"
              onClick={async () => {
                if (!window.confirm('Are you sure you want to delete this purchase order? This action cannot be undone.')) {
                  return;
                }

                try {
                  setIsDeleting(true);
                  const token = await getFirebaseToken();
                  await api.delete(`/api/inventory/purchase-orders/${id}`, {
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  });
                  
                  toast.success('Purchase order deleted successfully');
                  router.push('/inventory/purchase-orders');
                } catch (error: any) {
                  console.error('Error deleting purchase order:', error);
                  toast.error(error.response?.data?.message || 'Failed to delete purchase order');
                } finally {
                  setIsDeleting(false);
                }
              }}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete Order
            </Button>
          )}
        </div>
      </div>

      <Separator />

      {/* Main Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Left Column */}
        <div className="space-y-6">
          <div className="bg-card rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Supplier Information</h2>
            <div className="space-y-2">
              <p><span className="font-medium">Name:</span> {purchaseOrder.supplier.name}</p>
              <p><span className="font-medium">Code:</span> {purchaseOrder.supplier.code}</p>
              {purchaseOrder.supplier.email && (
                <p><span className="font-medium">Email:</span> {purchaseOrder.supplier.email}</p>
              )}
              {purchaseOrder.supplier.phone && (
                <p><span className="font-medium">Phone:</span> {purchaseOrder.supplier.phone}</p>
              )}
              {purchaseOrder.supplier.address && (
                <p><span className="font-medium">Address:</span> {purchaseOrder.supplier.address}</p>
              )}
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Order Details</h2>
            <div className="space-y-2">
              <p><span className="font-medium">Order Number:</span> {purchaseOrder.orderNumber}</p>
              <p><span className="font-medium">Created Date:</span> {new Date(purchaseOrder.createdAt).toLocaleDateString()}</p>
              {purchaseOrder.updatedAt && purchaseOrder.updatedAt !== purchaseOrder.createdAt && (
                <p><span className="font-medium">Last Updated:</span> {new Date(purchaseOrder.updatedAt).toLocaleDateString()}</p>
              )}
              <p><span className="font-medium">Payment Terms:</span> {purchaseOrder.paymentTerms}</p>
              {purchaseOrder.paymentReference && (
                <p><span className="font-medium">Payment Reference:</span> {purchaseOrder.paymentReference}</p>
              )}
              {purchaseOrder.invoiceNumber && (
                <p><span className="font-medium">Invoice Number:</span> {purchaseOrder.invoiceNumber}</p>
              )}
              {purchaseOrder.invoiceDate && (
                <p><span className="font-medium">Invoice Date:</span> {new Date(purchaseOrder.invoiceDate).toLocaleDateString()}</p>
              )}
              {purchaseOrder.bankPaymentReference && (
                <p><span className="font-medium">Bank Payment Reference:</span> {purchaseOrder.bankPaymentReference}</p>
              )}
              {purchaseOrder.bankPaymentReferenceDate && (
                <p><span className="font-medium">Bank Payment Reference Date:</span> {new Date(purchaseOrder.bankPaymentReferenceDate).toLocaleDateString()}</p>
              )}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          <div className="bg-card rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Status Information</h2>
            <div className="space-y-4">
              <div>
                <Badge 
                  variant={purchaseOrder.status === 'COMPLETED' ? 'default' : 
                          purchaseOrder.status === 'CANCELLED' ? 'destructive' : 
                          'secondary'}
                  className="text-sm"
                >
                  {purchaseOrder.status}
                </Badge>
              </div>
              <div className="pt-2">
                <p><span className="font-medium">Subtotal:</span> {formatCurrency(calculatedSubtotal)}</p>
                <p><span className="font-medium">Tax:</span> {formatCurrency(purchaseOrder.tax)}</p>
                <p className="text-lg font-semibold mt-2">
                  <span>Total:</span> {formatCurrency(calculatedTotal)}
                </p>
              </div>
            </div>
          </div>

          {purchaseOrder.notes && (
            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h2 className="text-xl font-semibold mb-4">Notes</h2>
              <p className="text-muted-foreground whitespace-pre-wrap">{purchaseOrder.notes}</p>
            </div>
          )}
        </div>
      </div>

      {/* Items Table */}
      <div className="bg-card rounded-lg p-6 shadow-sm mt-8">
        <h2 className="text-xl font-semibold mb-4">Order Items</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4">Item</th>
                <th className="text-right py-3 px-4">Quantity</th>
                <th className="text-right py-3 px-4">Unit Price</th>
                <th className="text-right py-3 px-4">Total</th>
                <th className="text-left py-3 px-4">Notes</th>
              </tr>
            </thead>
            <tbody>
              {purchaseOrder.items.map((item: any) => (
                <tr key={item.id} className="border-b">
                  <td className="py-3 px-4">
                    <div>
                      <p className="font-medium">{item.item.name}</p>
                      <p className="text-sm text-muted-foreground">
                        SKU: {item.item.sku || 'N/A'}
                        {item.item.description && (
                          <span className="block mt-1">{item.item.description}</span>
                        )}
                      </p>
                    </div>
                  </td>
                  <td className="text-right py-3 px-4">
                    {item.quantity} {item.item.unitMetric?.displayName || item.item.unitMetric?.name || 'units'}
                  </td>
                  <td className="text-right py-3 px-4">{formatCurrency(item.unitPrice)}</td>
                  <td className="text-right py-3 px-4 font-medium">
                    {formatCurrency(item.total || (item.quantity * Number(item.unitPrice)))}
                  </td>
                  <td className="py-3 px-4">
                    {item.notes ? (
                      <p className="text-sm text-muted-foreground">{item.notes}</p>
                    ) : (
                      <span className="text-sm text-muted-foreground">-</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="border-t">
                <td colSpan={4} className="text-right py-3 px-4 font-medium">Subtotal:</td>
                <td className="text-right py-3 px-4 font-medium">{formatCurrency(calculatedSubtotal)}</td>
              </tr>
              <tr>
                <td colSpan={4} className="text-right py-3 px-4 font-medium">Tax:</td>
                <td className="text-right py-3 px-4 font-medium">{formatCurrency(purchaseOrder.tax)}</td>
              </tr>
              <tr>
                <td colSpan={4} className="text-right py-3 px-4 font-medium">Additional Charge:</td>
                <td className="text-right py-3 px-4 font-medium">{formatCurrency(purchaseOrder.additionalCharge)}</td>
              </tr>
              <tr className="text-lg">
                <td colSpan={4} className="text-right py-3 px-4 font-bold">Total:</td>
                <td className="text-right py-3 px-4 font-bold">{formatCurrency(calculatedTotal)}</td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
  );
}
