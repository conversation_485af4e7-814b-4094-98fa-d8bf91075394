'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { Plus, Trash2, Save } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/providers/auth-provider';
import api from '@/lib/api';

// Define the shape of an adjustment item
interface AdjustmentItem {
  id: string;
  itemId: string;
  itemName: string;
  currentStock: number;
  adjustmentQuantity: number;
  adjustmentType: 'damage' | 'loss' | 'correction' | 'expiry';
  reason: string;
  unitMetric?: string; // Display name of the unit metric
}

// Define the shape of the API response
interface ApiResponse {
  success: boolean;
  message?: string;
  data?: any;
}

// Define the shape of the API error
interface ApiError {
  response: {
    status: number;
    data: ApiResponse;
  };
  message: string;
}

export default function NewAdjustmentPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<any[]>([]);
  const [selectedItems, setSelectedItems] = useState<AdjustmentItem[]>([]);
  const [reference, setReference] = useState('');
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [notes, setNotes] = useState('');
  const [filterText, setFilterText] = useState('');

  // Check authentication
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  // Check if user has inventory access
  useEffect(() => {
    console.log('User data:', user);
    console.log('Admin access:', user?.adminAccess);
    console.log('User role:', user?.role);
    
    if (user && !user.adminAccess?.includes('inventory') && user.role !== 'ADMIN' && user.role !== 'SUPER_ADMIN') {
      toast.error('You do not have access to this page');
      router.push('/dashboard');
      return;
    }
  }, [user, router]);

  // Fetch all inventory items
  useEffect(() => {
    const fetchItems = async () => {
      try {
        const response = await api.get('/api/inventory-items', {
          params: {
            isActive: true
          }
        });
        if (response.data?.data?.items) {
          setItems(response.data.data.items);
        } else {
          console.error('Unexpected response format:', response.data);
          setItems([]);
        }
      } catch (error: any) {
        console.error('Error fetching items:', error);
        toast.error('Failed to fetch items');
        setItems([]);
      }
    };

    fetchItems();
  }, []);

  // Add item to adjustment
  const addItem = (item: any) => {
    // Check if item is already added
    if (selectedItems.some(selected => selected.itemId === item.id)) {
      toast.error('This item is already added to the adjustment');
      return;
    }

    const newItem: AdjustmentItem = {
      id: crypto.randomUUID(),
      itemId: item.id,
      itemName: item.name,
      currentStock: item.currentStock,
      adjustmentQuantity: 0,
      adjustmentType: 'correction',
      reason: '',
      unitMetric: item.unitMetric?.displayName || 'units',
    };

    setSelectedItems([...selectedItems, newItem]);
  };

  // Remove item from adjustment
  const removeItem = (id: string) => {
    setSelectedItems(selectedItems.filter(item => item.id !== id));
  };

  // Update item details
  const updateItem = (id: string, field: keyof AdjustmentItem, value: any) => {
    setSelectedItems(selectedItems.map(item => {
      if (item.id === id) {
        return { ...item, [field]: value };
      }
      return item;
    }));
  };

  // Filter items based on search text
  const filteredItems = items.filter(item => 
    item.name.toLowerCase().includes(filterText.toLowerCase()) ||
    item.sku?.toLowerCase().includes(filterText.toLowerCase())
  );

  // Save adjustment
  const handleSave = async () => {
    try {
      // Validate form data
      const errors: string[] = [];
      
      if (!reference.trim()) {
        errors.push('Reference is required');
      }
      
      if (!date) {
        errors.push('Date is required');
      }
      
      if (selectedItems.length === 0) {
        errors.push('At least one item is required');
      }

      // Check for maximum items limit
      if (selectedItems.length > 100) {
        errors.push('Maximum 100 items allowed per adjustment. Please split into multiple adjustments.');
      }

      // Validate each item
      selectedItems.forEach((item, index) => {
        if (!item.adjustmentQuantity || item.adjustmentQuantity === 0) {
          errors.push(`Adjustment quantity is required for item ${index + 1}: ${item.itemName}`);
        }
        if (!item.reason?.trim()) {
          errors.push(`Reason is required for item ${index + 1}: ${item.itemName}`);
        }
        if (!item.adjustmentType) {
          errors.push(`Adjustment type is required for item ${index + 1}: ${item.itemName}`);
        }
      });

      if (errors.length > 0) {
        // Show each error in a toast
        errors.forEach(error => toast.error(error));
        return;
      }

      setLoading(true);

      // Show progress message for large batches
      if (selectedItems.length > 20) {
        toast.loading(`Processing ${selectedItems.length} items... This may take a moment.`, {
          duration: 5000,
        });
      }

      // Format payload to match backend expectations
      const payload = {
        reference: reference.trim(),
        date: date,
        notes: notes?.trim() || undefined,
        items: selectedItems.map(item => ({
          itemId: item.itemId,
          quantity: Number(item.adjustmentQuantity),
          type: item.adjustmentType.toUpperCase(),
          reason: item.reason?.trim() || '',
        })),
      };

      console.log(`Sending adjustment payload with ${payload.items.length} items:`, {
        reference: payload.reference,
        date: payload.date,
        itemCount: payload.items.length,
        timestamp: new Date().toISOString()
      });

      const startTime = Date.now();
      const response = await api.post('/api/inventory-adjustments', payload, {
        timeout: 120000, // 2 minutes timeout for large batches
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const processingTime = Date.now() - startTime;
      console.log(`API Response received in ${processingTime}ms:`, {
        success: response.data?.success,
        itemsProcessed: response.data?.meta?.itemsProcessed,
        processingTimeMs: response.data?.meta?.processingTimeMs
      });

      if (!response.data?.success) {
        throw new Error(response.data?.message || 'Failed to save adjustment');
      }

      // Show success message with processing details
      const meta = response.data.meta;
      if (meta && meta.itemsProcessed > 1) {
        toast.success(`Adjustment saved successfully! Processed ${meta.itemsProcessed} items in ${Math.round(meta.processingTimeMs / 1000)}s`);
      } else {
        toast.success('Adjustment saved successfully');
      }
      
      router.push('/inventory/adjustments');
    } catch (error: any) {
      console.error('Error saving adjustment:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data,
        itemCount: selectedItems.length,
        timestamp: new Date().toISOString()
      });

      // Show more detailed error messages
      if (error.response?.status === 400) {
        toast.error(error.response.data?.message || 'Invalid data. Please check all fields.');
      } else if (error.response?.status === 401) {
        toast.error('Session expired. Please login again.');
        router.push('/login');
      } else if (error.response?.status === 403) {
        toast.error('You do not have permission to create adjustments.');
        router.push('/inventory/adjustments');
      } else if (error.response?.status === 404) {
        toast.error('API endpoint not found. Please contact support.');
      } else if (error.response?.status === 408) {
        toast.error('Request timeout. Please try with fewer items or contact support.');
      } else if (error.response?.status === 500) {
        toast.error('Server error. Please try again later.');
      } else if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        toast.error(`Request timeout with ${selectedItems.length} items. Please try with fewer items or contact support.`);
      } else {
        toast.error(error.response?.data?.message || error.message || 'Failed to save adjustment');
      }
    } finally {
      setLoading(false);
    }
  };

  // Show loading state while checking auth
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Don't render anything if not authenticated
  if (!user) {
    return null;
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">New Inventory Adjustment</h1>
          <p className="text-sm text-gray-500">Add new inventory adjustments</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/inventory/adjustments')}
            type="button"
          >
            Cancel
          </Button>
          <Button
            variant="default"
            onClick={handleSave}
            disabled={loading}
            type="button"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {loading ? 'Saving...' : 'Save Adjustment'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="reference" className="flex items-center gap-1">
                  Reference Number
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="reference"
                  value={reference}
                  onChange={(e) => setReference(e.target.value)}
                  placeholder="Enter reference number"
                  required
                />
              </div>
              <div>
                <Label htmlFor="date" className="flex items-center gap-1">
                  Date
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add any notes about this adjustment (optional)"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="space-y-4">
              <div>
                <Label>Filter Items</Label>
                <Input
                  value={filterText}
                  onChange={(e) => setFilterText(e.target.value)}
                  placeholder="Filter items by name or SKU..."
                />
              </div>
              <div className="border rounded-md max-h-[300px] overflow-y-auto">
                {filteredItems.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    {items.length === 0 ? 'Loading items...' : 'No items found'}
                  </div>
                ) : (
                  filteredItems.map((item) => (
                    <div
                      key={item.id}
                      className="p-2 hover:bg-gray-50 cursor-pointer flex items-center justify-between border-b last:border-b-0"
                      onClick={() => addItem(item)}
                    >
                      <div>
                        <div className="font-medium">{item.name}</div>
                        <div className="text-sm text-gray-500">
                          SKU: {item.sku || 'N/A'} | Stock: {item.currentStock} {item.unitMetric?.displayName || 'units'}
                        </div>
                      </div>
                      <Plus className="w-4 h-4" />
                    </div>
                  ))
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            <h2 className="text-lg font-medium">Adjustment Items</h2>
            {selectedItems.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No items added. Select items from the list above.
              </div>
            ) : (
              <div className="space-y-4">
                {selectedItems.map((item) => (
                  <div key={item.id} className="border rounded-md p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <div className="font-medium">{item.itemName}</div>
                        <div className="text-sm text-gray-500">
                          Current Stock: {item.currentStock} {item.unitMetric || 'units'}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeItem(item.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label className="flex items-center gap-1">
                          Adjustment Type
                          <span className="text-red-500">*</span>
                        </Label>
                        <Select
                          value={item.adjustmentType}
                          onValueChange={(value) => updateItem(item.id, 'adjustmentType', value)}
                          required
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="damage">Damage</SelectItem>
                            <SelectItem value="loss">Loss</SelectItem>
                            <SelectItem value="correction">Correction</SelectItem>
                            <SelectItem value="expiry">Expiry</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label className="flex items-center gap-1">
                          Quantity ({item.unitMetric || 'units'})
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          type="number"
                          value={item.adjustmentQuantity}
                          onChange={(e) => updateItem(item.id, 'adjustmentQuantity', e.target.value)}
                          placeholder={`Enter quantity in ${(item.unitMetric || 'units').toLowerCase()}`}
                          required
                        />
                      </div>
                      <div>
                        <Label>Reason</Label>
                        <Input
                          value={item.reason}
                          onChange={(e) => updateItem(item.id, 'reason', e.target.value)}
                          placeholder="Enter reason (optional)"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
