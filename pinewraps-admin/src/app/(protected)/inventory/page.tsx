'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useRouter } from 'next/navigation';
import {
  Package2,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  MoreVertical,
  PackageOpen,
  AlertTriangle,
  CheckCircle2,
  Download,
  Upload,
  Archive,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Heading } from '@/components/ui/heading';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from '@/lib/toast';
import api from '@/lib/api';
import { InventoryForm } from '@/components/inventory/InventoryForm';
import { getToken } from '@/lib/get-token';

interface UnitMetric {
  id: string;
  name: string;
  displayName: string;
  isActive: boolean;
  isDefault: boolean;
}

interface InventoryItem {
  id: string;
  name: string;
  description?: string;
  sku?: string;
  minQuantity: number;
  currentStock: number;
  unitMetric?: UnitMetric;
  unitMetricId: string;
  conversionRate?: number;
  isActive: boolean;
  categoryId?: string;
  category?: {
    id: string;
    name: string;
  };
  adjustments?: any[];
  stockHistory?: any[];
  purchaseOrderItems?: any[];
}

interface Category {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

export default function InventoryPage() {
  const router = useRouter();
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [stockFilter, setStockFilter] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [deleting, setDeleting] = useState(false);

  const forceCloseDialogs = () => {
    setIsFormOpen(false);
    setIsDeleteDialogOpen(false);
    setSelectedItem(null);
    setSubmitting(false);
    setDeleting(false);

    // Force remove any modal overlays
    setTimeout(() => {
      const overlays = document.querySelectorAll('[data-radix-dialog-overlay]');
      overlays.forEach(overlay => overlay.remove());

      const contents = document.querySelectorAll('[data-radix-dialog-content]');
      contents.forEach(content => content.remove());

      // Remove any remaining modal backdrops
      const backdrops = document.querySelectorAll('.fixed.inset-0.z-50');
      backdrops.forEach(backdrop => backdrop.remove());
    }, 50);
  };

  // Cleanup effect to ensure proper state management
  useEffect(() => {
    return () => {
      // Reset all dialog states on unmount
      forceCloseDialogs();
    };
  }, []);

  // Emergency cleanup effect - force close dialogs if they get stuck
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        forceCloseDialogs();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Body scroll lock when modals are open
  useEffect(() => {
    if (isFormOpen || isDeleteDialogOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isFormOpen, isDeleteDialogOpen]);

  const fetchInventory = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/inventory-items', {
        params: {
          search: searchQuery || undefined,
          categoryId: selectedCategory === 'all' ? undefined : selectedCategory,
          isActive: statusFilter === 'active' ? true : statusFilter === 'inactive' ? false : undefined,
          stockBelow: stockFilter || undefined
        }
      });
      setItems(response.data.data.items || []);
    } catch (error: any) {
      console.error('Error fetching inventory:', error);
      toast.error(error.response?.data?.message || 'Failed to fetch inventory items');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await api.get('/api/inventory-categories');
      setCategories(response.data.data || []);
    } catch (error: any) {
      console.error('Error fetching categories:', error);
      toast.error(error.response?.data?.message || 'Failed to fetch categories');
      setCategories([]);
    }
  };

  useEffect(() => {
    fetchInventory();
    fetchCategories();
  }, [searchQuery, statusFilter, selectedCategory, stockFilter]);

  const handleSubmit = async (data: any) => {
    try {
      setSubmitting(true);
      if (selectedItem) {
        await api.patch(`/api/inventory-items/${selectedItem.id}`, data);
        toast.crud.updated('Inventory Item');
      } else {
        await api.post('/api/inventory-items', data);
        toast.crud.created('Inventory Item');
      }
      fetchInventory();
      forceCloseDialogs();
    } catch (error: any) {
      console.error('Error saving item:', error);
      const errorMessage = error.response?.data?.error || 'Failed to save item';
      toast.crud.createError('Inventory Item', errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleArchiveItem = async (id: string) => {
    try {
      setDeleting(true);
      await api.patch(`/api/inventory-items/${id}/archive`);
      toast.success('Item archived successfully');
      fetchInventory();
      forceCloseDialogs();
    } catch (error: any) {
      console.error('Error archiving item:', error);
      toast.error(error.response?.data?.error || 'Failed to archive item');
    } finally {
      setDeleting(false);
    }
  };

  const handleDeleteItem = async (id: string) => {
    try {
      setDeleting(true);
      await api.delete(`/api/inventory-items/${id}`);
      toast.crud.deleted('Inventory Item');
      fetchInventory();
      forceCloseDialogs();
    } catch (error: any) {
      console.error('Error deleting item:', error);
      if (error.response?.data?.error?.includes('associated records')) {
        // If item has history, offer to archive instead
        toast.error('Cannot delete item with history. Use Archive instead.');
      } else {
        toast.error(error.response?.data?.error || 'Failed to delete item');
      }
    } finally {
      setDeleting(false);
    }
  };

  const onDelete = (item: InventoryItem) => {
    setSelectedItem(item);
    setIsDeleteDialogOpen(true);
  };

  const filteredItems = items.filter(item => {
    if (statusFilter === 'all') return true;
    if (statusFilter === 'active') return item.isActive;
    if (statusFilter === 'inactive') return !item.isActive;
    return true;
  });

  const lowStockItems = items.filter(item => item.currentStock <= item.minQuantity);
  const activeItems = items.filter(item => item.isActive);

  const exportInventory = async () => {
    try {
      const response = await api.get('/api/inventory-items/export', {
        responseType: 'blob',
        headers: {
          Authorization: `Bearer ${await getToken()}`,
        },
      });
      
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'inventory-items.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      toast.error('Failed to export inventory items');
    }
  };

  const importInventory = async (file: File) => {
    try {
      const text = await file.text();
      // Split by newlines and filter out empty lines
      // Handle both Windows (CRLF) and Unix (LF) line endings
      const rows = text.split(/\r?\n/)
        .filter(row => row.trim())
        .map(row => {
          // Handle CSV fields that might contain commas within quotes
          const fields: string[] = [];
          let inQuotes = false;
          let currentField = '';
          
          for (let i = 0; i < row.length; i++) {
            const char = row[i];
            if (char === '"') {
              // Handle escaped quotes ("")
              if (i + 1 < row.length && row[i + 1] === '"') {
                currentField += '"';
                i++; // Skip the next quote
              } else {
                inQuotes = !inQuotes;
              }
            } else if (char === ',' && !inQuotes) {
              fields.push(currentField.trim());
              currentField = '';
            } else {
              currentField += char;
            }
          }
          
          // Add the last field
          fields.push(currentField.trim());
          
          // Remove surrounding quotes from each field
          return fields.map(field => field.replace(/^"(.*)"$/, '$1').trim());
        });
      
      const headers = rows[0];
      console.log('CSV Headers:', headers);
      
      const items = rows.slice(1)
        .filter(row => row.length === headers.length)
        .map(row => {
          const item: Record<string, any> = {};
          
          // First, log the raw row data for debugging
          console.log('Processing CSV row:', row);
          
          headers.forEach((header, index) => {
            const value = row[index];
            // Store with original header
            item[header] = value;
            // Store with normalized header (both with and without spaces)
            const normalizedHeader = header.toLowerCase().replace(/ /g, '');
            item[normalizedHeader] = value;

            // Special handling for numeric fields - be more careful with parsing
            if (['Current Stock', 'Min Quantity', 'Conversion Rate', 'currentstock', 'minquantity', 'conversionrate'].includes(header) ||
                normalizedHeader === 'currentstock' || normalizedHeader === 'minquantity' || normalizedHeader === 'conversionrate') {
              // Only parse if the value is not empty
              if (value !== '' && value !== null && value !== undefined) {
                const cleanedValue = String(value).replace(/[^\d.-]/g, '');
                const numValue = cleanedValue ? parseFloat(cleanedValue) : 0;
                const finalValue = isNaN(numValue) ? 0 : numValue;
                
                // Store the numeric value in multiple formats to ensure it's properly processed
                item[header] = finalValue;
                item[normalizedHeader] = finalValue;
                
                // Also store in camelCase format
                if (header === 'Current Stock' || normalizedHeader === 'currentstock') {
                  item.currentStock = finalValue;
                  item.currentstock = finalValue;
                  item['Current Stock'] = finalValue;
                  console.log(`Setting current stock to ${finalValue} (from ${value})`);
                } 
                else if (header === 'Min Quantity' || normalizedHeader === 'minquantity') {
                  item.minQuantity = finalValue;
                  item.minquantity = finalValue;
                  item['Min Quantity'] = finalValue;
                  console.log(`Setting min quantity to ${finalValue} (from ${value})`);
                }
                else if (header === 'Conversion Rate' || normalizedHeader === 'conversionrate') {
                  item.conversionRate = finalValue;
                  item.conversionrate = finalValue;
                  item['Conversion Rate'] = finalValue;
                  console.log(`Setting conversion rate to ${finalValue} (from ${value})`);
                }
              } else {
                const defaultValue = 0;
                item[header] = defaultValue;
                item[normalizedHeader] = defaultValue;
                
                // Also set in camelCase format
                if (header === 'Current Stock' || normalizedHeader === 'currentstock') {
                  item.currentStock = defaultValue;
                  item.currentstock = defaultValue;
                  item['Current Stock'] = defaultValue;
                } 
                else if (header === 'Min Quantity' || normalizedHeader === 'minquantity') {
                  item.minQuantity = defaultValue;
                  item.minquantity = defaultValue;
                  item['Min Quantity'] = defaultValue;
                }
                else if (header === 'Conversion Rate' || normalizedHeader === 'conversionrate') {
                  item.conversionRate = defaultValue;
                  item.conversionrate = defaultValue;
                  item['Conversion Rate'] = defaultValue;
                }
              }
            }

            // Special handling for boolean fields
            if (['Active', 'Is Active', 'isactive'].includes(header) || normalizedHeader === 'isactive') {
              item[header] = value === 'Yes' || value === 'yes' || value === 'true' || value === 'TRUE' || value === 'True' || value === '1';
              item[normalizedHeader] = item[header];
            }
            
            // Special handling for unit metric fields
            if (['Unit Metric', 'unitmetric'].includes(header) || normalizedHeader === 'unitmetric') {
              // Make sure we preserve the original unit metric value in multiple formats
              // This ensures the backend can find it regardless of the field name used
              const unitMetricValue = value || 'unit';
              item['unitmetric'] = unitMetricValue;
              item['Unit Metric'] = unitMetricValue;
              item['unitMetric'] = unitMetricValue;
              item.unitMetric = unitMetricValue;
              
              // Also store the unit metric in lowercase to help with matching
              const lowerUnitMetric = String(unitMetricValue).toLowerCase();
              item['unitmetric_lower'] = lowerUnitMetric;
              
              console.log('Setting unit metric:', {
                original: value,
                stored: unitMetricValue,
                lowercase: lowerUnitMetric
              });
            }
            
            // Special handling for unit metric display fields
            if (['Unit Metric Display', 'unitmetricdisplay'].includes(header) || normalizedHeader === 'unitmetricdisplay') {
              // Make sure we preserve the original unit metric display value in multiple formats
              const displayValue = value || 'Unit';
              item['unitmetricdisplay'] = displayValue;
              item['Unit Metric Display'] = displayValue;
              item['unitMetricDisplay'] = displayValue;
              item.unitMetricDisplay = displayValue;
              
              console.log('Setting unit metric display:', {
                original: value,
                stored: displayValue
              });
            }

            // Special handling for category fields
            if (['Category', 'category'].includes(header) || normalizedHeader === 'category') {
              // Store category in multiple formats to ensure backend can find it
              item.category = value || '';
              item['category'] = value || '';
              item['Category'] = value || '';
              console.log('Setting category:', value);
            }
          });

          // Ensure essential fields have proper defaults
          if (!item.currentstock && !item['Current Stock'] && !item.currentStock) {
            item.currentstock = 0;
          }
          if (!item.minquantity && !item['Min Quantity'] && !item.minQuantity) {
            item.minquantity = 0;
          }
          
          return item;
        });

      // Log the parsed items for debugging
      console.log('Parsed CSV items:', items);

      if (items.length === 0) {
        throw new Error('No valid items found in CSV');
      }

      // Ensure all items have the essential fields properly set
      const processedItems = items.map(item => {
        // Ensure stock field is properly set
        if (!item.currentstock && !item['Current Stock'] && !item.currentStock) {
          item.currentstock = 0;
          item['Current Stock'] = 0;
        }

        // Ensure min quantity field is properly set
        if (!item.minquantity && !item['Min Quantity'] && !item.minQuantity) {
          item.minquantity = 0;
          item['Min Quantity'] = 0;
        }

        // Ensure unit metric field is properly set
        if (!item.unitmetric && !item['Unit Metric'] && !item.unitMetric) {
          item.unitmetric = 'unit';
          item['Unit Metric'] = 'unit';
        }

        // Ensure category field is properly set
        if (!item.category && !item.Category) {
          item.category = '';
          item.Category = '';
        }

        return item;
      });

      console.log('Importing processed items:', processedItems);

      const response = await api.post('/api/inventory-items/import', { items: processedItems }, {
        headers: {
          Authorization: `Bearer ${await getToken()}`,
        },
      });

      toast.success(`Import complete. Created: ${response.data.results.created}, Updated: ${response.data.results.updated}`);

      if (response.data.results.failed > 0) {
        console.error('Import errors:', response.data.results.errors);
        toast.error(`${response.data.results.failed} items failed to import. Check console for details.`);
      }

      // Refresh the data
      fetchInventory();
    } catch (error: any) {
      console.error('Import error:', error);
      toast.error(error.message || 'Failed to import inventory items');
    }
  };

  const canDelete = !selectedItem?.adjustments?.length && 
                   !selectedItem?.stockHistory?.length && 
                   !selectedItem?.purchaseOrderItems?.length;

  return (
    <div className="flex-1 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Heading 
            title="Inventory" 
            description="Manage your inventory items and stock levels" 
          />
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            className="ml-auto"
            onClick={() => exportInventory()}
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const input = document.createElement('input');
              input.type = 'file';
              input.accept = '.csv';
              input.onchange = (e) => {
                const file = (e.target as HTMLInputElement).files?.[0];
                if (file) importInventory(file);
              };
              input.click();
            }}
          >
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button onClick={() => setIsFormOpen(true)} size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Add Item
          </Button>
        </div>
      </div>
      <Separator />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Items
            </CardTitle>
            <Package2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{items.length}</div>
            <p className="text-xs text-muted-foreground">
              {activeItems.length} active items
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Low Stock Items
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{lowStockItems.length}</div>
            <p className="text-xs text-muted-foreground">
              Items below minimum quantity
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Stock Value
            </CardTitle>
            <PackageOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {items.reduce((sum, item) => sum + item.currentStock, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total items in stock
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>
            Filter and search through your inventory items
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select
              value={statusFilter}
              onValueChange={setStatusFilter}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="w-[180px]">
              <Input
                type="number"
                placeholder="Stock below"
                value={stockFilter}
                onChange={(e) => setStockFilter(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>SKU</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Stock</TableHead>
                <TableHead>Unit</TableHead>
                <TableHead>Min Qty</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredItems.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.name}</TableCell>
                  <TableCell>{item.sku || '-'}</TableCell>
                  <TableCell>{item.category?.name || '-'}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={item.currentStock <= item.minQuantity ? "destructive" : "default"}
                      className="font-mono"
                    >
                      {item.currentStock}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-muted-foreground text-sm">
                      {item.unitMetric ? item.unitMetric.displayName : ''}
                      {item.conversionRate && ` (1 = ${item.conversionRate})`}
                    </span>
                  </TableCell>
                  <TableCell>{item.minQuantity}</TableCell>
                  <TableCell>
                    <Badge variant={item.isActive ? "default" : "secondary"}>
                      {item.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedItem(item);
                            setIsFormOpen(true);
                          }}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        {!canDelete ? (
                          <DropdownMenuItem
                            onClick={() => onDelete(item)}
                          >
                            <Archive className="mr-2 h-4 w-4" />
                            Archive
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => onDelete(item)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
              {filteredItems.length === 0 && (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    No items found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      {isFormOpen && createPortal(
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => {
              if (!submitting) {
                forceCloseDialogs();
              }
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-lg shadow-lg max-w-3xl w-full mx-4 max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">
                {selectedItem ? 'Edit Item' : 'Add Item'}
              </h2>
            </div>

            {/* Content */}
            <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="p-6">
                <InventoryForm
                  key={selectedItem?.id || 'new'}
                  initialData={selectedItem}
                  categories={categories}
                  onSubmit={handleSubmit}
                  onCancel={() => {
                    if (!submitting) {
                      forceCloseDialogs();
                    }
                  }}
                  loading={submitting}
                />
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}

      {/* Delete/Archive Confirmation Modal */}
      {isDeleteDialogOpen && createPortal(
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => {
              if (!deleting) {
                forceCloseDialogs();
              }
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
            {/* Header */}
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">
                {canDelete ? 'Delete Item' : 'Archive Item'}
              </h2>
            </div>

            {/* Content */}
            <div className="p-6">
              <p className="text-sm text-gray-600 mb-6">
                {canDelete ? (
                  <>
                    Are you sure you want to delete "{selectedItem?.name}"? This action cannot be undone.
                  </>
                ) : (
                  <>
                    This item has associated records (adjustments, stock history, or purchase orders) and cannot be deleted.
                    Would you like to archive "{selectedItem?.name}" instead?
                    Archived items will be hidden from the active inventory but their history will be preserved.
                  </>
                )}
              </p>

              {/* Footer */}
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    if (!deleting) {
                      forceCloseDialogs();
                    }
                  }}
                  disabled={deleting}
                >
                  Cancel
                </Button>
                {canDelete ? (
                  <Button
                    variant="destructive"
                    onClick={() => selectedItem && handleDeleteItem(selectedItem.id)}
                    disabled={deleting}
                  >
                    {deleting ? 'Deleting...' : 'Delete'}
                  </Button>
                ) : (
                  <Button
                    variant="default"
                    onClick={() => selectedItem && handleArchiveItem(selectedItem.id)}
                    disabled={deleting}
                  >
                    {deleting ? 'Archiving...' : 'Archive'}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
}
