'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import staffReportsService, { StaffPerformanceMetrics } from '@/services/staff-reports.service';

interface StaffPerformanceTableProps {
  data: StaffPerformanceMetrics[];
  isLoading: boolean;
}

export default function StaffPerformanceTable({ data, isLoading }: StaffPerformanceTableProps) {
  const router = useRouter();

  const handleStaffClick = (staffId: string) => {
    router.push(`/staff-reports/${staffId}`);
  };
  const [sortField, setSortField] = React.useState<keyof StaffPerformanceMetrics>('productivityScore');
  const [sortDirection, setSortDirection] = React.useState<'asc' | 'desc'>('desc');

  const handleSort = (field: keyof StaffPerformanceMetrics) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const sortedData = React.useMemo(() => {
    if (!data) return [];
    
    return [...data].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      return 0;
    });
  }, [data, sortField, sortDirection]);

  const SortButton = ({ field, children }: { field: keyof StaffPerformanceMetrics; children: React.ReactNode }) => (
    <Button
      variant="ghost"
      size="sm"
      className="h-auto p-0 font-medium"
      onClick={() => handleSort(field)}
    >
      {children}
      {sortField === field ? (
        sortDirection === 'asc' ? (
          <ArrowUp className="ml-1 h-3 w-3" />
        ) : (
          <ArrowDown className="ml-1 h-3 w-3" />
        )
      ) : (
        <ArrowUpDown className="ml-1 h-3 w-3 opacity-50" />
      )}
    </Button>
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Staff Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(10)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Staff Performance Details</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full min-w-[800px] table-auto">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3 w-[200px]">
                  <SortButton field="staffName">Staff Name</SortButton>
                </th>
                <th className="text-left p-3 w-[120px]">
                  <SortButton field="department">Department</SortButton>
                </th>
                <th className="text-left p-3 w-[80px]">
                  <SortButton field="ordersCompleted">Orders</SortButton>
                </th>
                <th className="text-left p-3 w-[100px]">
                  <SortButton field="averageProcessingTime">Avg Time</SortButton>
                </th>
                <th className="text-left p-3 w-[90px]">
                  <SortButton field="ordersSentBack">Sent Back</SortButton>
                </th>
                <th className="text-left p-3 w-[90px]">
                  <SortButton field="qualityScore">Quality</SortButton>
                </th>
                <th className="text-left p-3 w-[110px]">
                  <SortButton field="productivityScore">Productivity</SortButton>
                </th>
                <th className="text-left p-3 w-[100px]">
                  <SortButton field="ordersPerHour">Orders/Hour</SortButton>
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedData.map((staff, index) => {
                const qualityRating = staffReportsService.getPerformanceRating(staff.qualityScore);
                const productivityRating = staffReportsService.getPerformanceRating(staff.productivityScore);
                
                return (
                  <tr key={`${staff.staffId}-${index}`} className="border-b hover:bg-muted/50">
                    <td className="p-3 font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleStaffClick(staff.staffId)}
                          className="text-left hover:text-primary hover:underline transition-colors cursor-pointer"
                        >
                          {staff.staffName}
                        </button>
                        {staff.isMultiRole && (
                          <Badge variant="secondary" className="text-xs">
                            Multi-Role
                          </Badge>
                        )}
                      </div>
                    </td>
                    <td className="p-3">
                      <Badge 
                        variant="outline"
                        style={{ 
                          borderColor: staffReportsService.getDepartmentColor(staff.department),
                          color: staffReportsService.getDepartmentColor(staff.department)
                        }}
                      >
                        {staffReportsService.getDepartmentDisplayName(staff.department)}
                      </Badge>
                    </td>
                    <td className="p-3 font-medium">{staff.ordersCompleted}</td>
                    <td className="p-3">{staffReportsService.formatProcessingTime(staff.averageProcessingTime)}</td>
                    <td className="p-3">
                      {staff.ordersSentBack > 0 ? (
                        <Badge variant="destructive">{staff.ordersSentBack}</Badge>
                      ) : (
                        <span className="text-muted-foreground">0</span>
                      )}
                    </td>
                    <td className="p-3">
                      <div className="flex items-center space-x-2">
                        <span className={qualityRating.color}>
                          {staff.qualityScore.toFixed(1)}%
                        </span>
                        <Badge variant="outline" className={qualityRating.color}>
                          {qualityRating.label}
                        </Badge>
                      </div>
                    </td>
                    <td className="p-3">
                      <div className="flex items-center space-x-2">
                        <span className={productivityRating.color}>
                          {staff.productivityScore.toFixed(1)}%
                        </span>
                        <Badge variant="outline" className={productivityRating.color}>
                          {productivityRating.label}
                        </Badge>
                      </div>
                    </td>
                    <td className="p-3">{staff.ordersPerHour.toFixed(1)}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No staff performance data available for the selected filters.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
